package com.medusa.gruul.addon.supplier.mp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.addon.supplier.modules.goods.service.EditSupplierGoodsService;
import com.medusa.gruul.addon.supplier.mp.entity.SupplierGoods;
import com.medusa.gruul.addon.supplier.mp.rocketmq.goods.GoodsValuationMethod;
import com.medusa.gruul.addon.supplier.mp.rocketmq.goods.SupplierGoodsBusinessType;
import com.medusa.gruul.addon.supplier.mp.rocketmq.goods.SupplierGoodsMessage;
import com.medusa.gruul.addon.supplier.mp.service.ISupplierGoodsMessageService;
import com.medusa.gruul.addon.supplier.mp.service.ISupplierGoodsService;
import com.medusa.gruul.common.ipaas.MdmConfig;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.goods.SupplierGoodsDetailBatchQueryClient;
import com.medusa.gruul.common.ipaas.client.team.StoreQueryByTeamInfoIdsClient;
import com.medusa.gruul.common.ipaas.model.goods.*;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsReqDTO;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsRspVO;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.ProductType;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.dto.SkuDTO;
import com.medusa.gruul.storage.api.dto.SpecDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * mdm供应商商品消息
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierGoodsMessageServiceImpl implements ISupplierGoodsMessageService {
    /**
     * 供应商商品消息时间戳缓存过期时间（天）
     */
    private static final int SUPPLIER_GOODS_MESSAGE_TIMESTAMP_EXPIRE_DAYS = 7;
    /**
     * 供应商商品消息时间戳缓存前缀
     */
    private static final String SUPPLIER_GOODS_MESSAGE_TIMESTAMP_PREFIX = "supplier:goods:message:timestamp:";
    /**
     * 默认商品图片
     */
    private static final String DEFAULT_IMG_URL = "https://m-oss.holderzone.cn/fsgen/15876910489a0c1543451be7130e7b53%E5%95%86%E5%93%81%E9%BB%98%E8%AE%A4%E5%9B%BE%E7%89%87.png";

    private final IpaasClientSupport ipaasClientSupport;
    private final MdmConfig mdmConfig;
    private final ISupplierGoodsService supplierGoodsService;
    private final ShopRpcService shopRpcService;
    private final StorageRpcService storageRpcService;
    private final EditSupplierGoodsService editSupplierGoodsService;

    @Override
    @Redisson(value = GoodsConstant.SUPPLIER_GOODS_MESSAGE_PROCESS_LOCK, key = "#message.enterpriseId + ':' + #message.storeId", waitTime = 30, leaseTime = 300)
    public void processSupplierGoodsMessage(SupplierGoodsMessage message) {
        // 1. 验证消息
        if (!validateMessage(message)) {
            return;
        }

        // 2. 获取供应商店铺信息
        Shop shop = getSupplierShopInfo(message.getStoreId());
        if (shop == null) {
            return;
        }

        // 3. 设置系统上下文
        setSystemContext(message.getEnterpriseId(), shop);
        message.setMallShopId(shop.getId());

        // 4. 处理商品消息
        List<Long> mdmGoodsIds = message.getGoodsId();
        if (CollUtil.isEmpty(mdmGoodsIds)) {
            log.info("【供应商商品消息同步】=======商品ID列表为空");
            return;
        }

        // 获取消息时间戳
        Long messageTimestamp = message.getTimestamp();

        // 过滤需要处理的商品（基于时间戳）
        List<Long> filteredGoodsIds = mdmGoodsIds.stream()
                .filter(mdmGoodsId -> shouldProcessSupplierGoodsMessage(mdmGoodsId, messageTimestamp))
                .toList();
        if (CollUtil.isEmpty(filteredGoodsIds)) {
            log.info("【供应商商品消息同步】=======所有商品消息都已过期，跳过处理");
            return;
        }

        int totalSize = filteredGoodsIds.size();
        log.info("【供应商商品消息同步】=======开始处理商品，总数: {}", totalSize);
        processSupplierGoodsBatch(message, filteredGoodsIds, messageTimestamp);
        log.info("【供应商商品消息同步】=======商品处理完成，总数: {}", totalSize);
    }

    /**
     * 验证消息
     */
    private boolean validateMessage(SupplierGoodsMessage message) {
        if (message.getStoreId() == null
                || message.getEnterpriseId() == null) {
            log.info("【供应商商品消息同步】=======消息参数无效: mdmShopId/enterpriseId为空");
            return false;
        }

        SupplierGoodsBusinessType supplierGoodsBusinessType = SupplierGoodsBusinessType.fromCode(message.getSupplierBusinessType());
        if (supplierGoodsBusinessType == null) {
            log.info("【供应商商品消息同步】=======无效的业务类型: businessType={}", message.getSupplierBusinessType());
            return false;
        }
        log.info("【供应商商品消息同步】=======业务类型: businessType={},{}", supplierGoodsBusinessType.getCode(), supplierGoodsBusinessType.getDesc());

        return true;
    }

    /**
     * 获取供应商店铺信息
     *
     * @param supplierStoreTeamInfoId ipaas得部门id
     */
    private Shop getSupplierShopInfo(Long supplierStoreTeamInfoId) {
        List<StoreQueryByTeamInfoIdsRspVO> ipaasStores = new StoreQueryByTeamInfoIdsClient(ipaasClientSupport)
                .execute(new StoreQueryByTeamInfoIdsReqDTO().setTeamInfoIds(Collections.singletonList(supplierStoreTeamInfoId)));
        if (CollUtil.isEmpty(ipaasStores)) {
            log.info("【供应商商品消息同步】=======零售店铺不存在");
            return null;
        }

        StoreQueryByTeamInfoIdsRspVO ipaasStore = ipaasStores.get(0);
        List<Shop> shops = shopRpcService.listShopsByIpaasStoreIds(Collections.singletonList(ipaasStore.getId()));
        if (CollUtil.isEmpty(shops)) {
            log.info("【供应商商品消息同步】=======商城店铺不存在");
            return null;
        }
        return shops.get(0);
    }

    /**
     * 设置系统上下文
     */
    private void setSystemContext(Long enterpriseId, Shop shop) {
        SystemContextHolder.set(new Systems()
                .setEnterpriseId(enterpriseId)
                .setPlatformId(shop.getPlatformId())
                .setShopId(shop.getId()));
    }

    /**
     * 处理商品批次
     */
    private void processSupplierGoodsBatch(SupplierGoodsMessage message, List<Long> batchGoodsIds, Long messageTimestamp) {
        // 1. 批量查询本地供应商商品信息
        Map<Long, SupplierGoods> supplierGoodstMap = getSupplierGoodsMap(batchGoodsIds);

        // 2. 批量查询MDM供应商商品详情
        List<ProviderStoreGoodsExtendChannelVO> mdmSupplierGoodsDetails = getMdmSupplierGoodsDetailBatch(
                message.getEnterpriseId(),
                message.getStoreId(),
                batchGoodsIds
        );

        // 3. 构建MDM商品Map
        Map<Long, ProviderStoreGoodsExtendChannelVO> mdmSupplierGoodsMap = CollUtil.isEmpty(mdmSupplierGoodsDetails) ?
                new HashMap<>() :
                mdmSupplierGoodsDetails.stream().collect(Collectors.toMap(ProviderStoreGoodsExtendChannelVO::getId, goods -> goods, (existing, replacement) -> existing));

        // 4. 处理每个商品
        batchGoodsIds.forEach(mdmGoodsId -> {
            processSingleSupplierGoods(message, mdmSupplierGoodsMap.get(mdmGoodsId), supplierGoodstMap.get(mdmGoodsId));
            // 更新商品消息时间戳缓存
            updateSupplierGoodsMessageTimestamp(mdmGoodsId, messageTimestamp);
        });
    }

    /**
     * 检查是否应该处理该供应商商品消息
     *
     * @param mdmGoodsId       商品ID
     * @param messageTimestamp 消息时间戳
     * @return true: 需要处理; false: 不需要处理（消息已过期）
     */
    private boolean shouldProcessSupplierGoodsMessage(Long mdmGoodsId, Long messageTimestamp) {
        String redisKey = getSupplierGoodsMessageTimestampKey(mdmGoodsId);
        Long lastTimestamp = RedisUtil.getCacheObject(redisKey);

        // 如果Redis中没有该商品的时间戳记录，或者当前消息时间戳大于上次处理的时间戳，则处理该消息
        return lastTimestamp == null || messageTimestamp > lastTimestamp;
    }

    /**
     * 更新商品消息时间戳缓存
     *
     * @param mdmGoodsId       商品ID
     * @param messageTimestamp 消息时间戳
     */
    private void updateSupplierGoodsMessageTimestamp(Long mdmGoodsId, Long messageTimestamp) {
        String redisKey = getSupplierGoodsMessageTimestampKey(mdmGoodsId);
        Long lastTimestamp = RedisUtil.getCacheObject(redisKey);

        // 双重检查，确保时间戳是最新的
        if (lastTimestamp == null || messageTimestamp > lastTimestamp) {
            RedisUtil.setCacheObject(redisKey, messageTimestamp, SUPPLIER_GOODS_MESSAGE_TIMESTAMP_EXPIRE_DAYS, TimeUnit.DAYS);
        }
    }

    /**
     * 获取供应商商品消息时间戳的Redis键
     *
     * @param mdmGoodsId 商品ID
     * @return Redis键
     */
    private String getSupplierGoodsMessageTimestampKey(Long mdmGoodsId) {
        return SUPPLIER_GOODS_MESSAGE_TIMESTAMP_PREFIX + mdmGoodsId;
    }

    /**
     * 获取供应商商品Map
     */
    private Map<Long, SupplierGoods> getSupplierGoodsMap(List<Long> mdmGoodsIds) {
        return supplierGoodsService.lambdaQuery()
                .in(SupplierGoods::getMdmGoodsId, mdmGoodsIds)
                .list()
                .stream()
                .collect(Collectors.toMap(SupplierGoods::getMdmGoodsId, Function.identity()));
    }

    /**
     * 处理单个供应商商品
     */
    private void processSingleSupplierGoods(SupplierGoodsMessage message, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, SupplierGoods mallSupplierGoodsDetail) {
        SupplierGoodsBusinessType businessType = SupplierGoodsBusinessType.fromCode(message.getSupplierBusinessType());

        switch (businessType) {
            case GOODS_DELETE -> handleGoodsDelete(mallSupplierGoodsDetail, message.getMallShopId());
            case GOODS_ENABLED -> handleGoodsStatusChange(mallSupplierGoodsDetail, ProductStatus.SELL_ON);
            case GOODS_DISABLED -> handleGoodsStatusChange(mallSupplierGoodsDetail, ProductStatus.SELL_OFF);
            case GOODS_CREATE, GOODS_UPDATE, GOODS_UPDATE_PIC ->
                    handleGoodsCreateOrUpdate(message, mdmSupplierGoodsDetail, mallSupplierGoodsDetail);
        }
    }

    /**
     * 处理商品删除
     */
    private void handleGoodsDelete(SupplierGoods mallSupplierGoodsDetail, Long mallShopId) {
        if (mallSupplierGoodsDetail == null) {
            log.info("【供应商商品消息同步】=======商品不存在，无需删除");
            return;
        }

        log.info("【供应商商品消息同步】=======删除商品: {}", mallSupplierGoodsDetail.getId());

        // 如果商品在售，先下架
        if (ProductStatus.SELL_ON == mallSupplierGoodsDetail.getSupplierProductStatus()) {
            handleGoodsStatusChange(mallSupplierGoodsDetail, ProductStatus.SELL_OFF);
        }

        // 删除商品
        editSupplierGoodsService.deleteMdmSupplierProductList(mallShopId, Set.of(mallSupplierGoodsDetail.getId()));
    }

    /**
     * 处理商品创建或更新
     */
    private void handleGoodsCreateOrUpdate(SupplierGoodsMessage message, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, SupplierGoods mallSupplierGoodsDetail) {
        // 验证MDM商品详情
        if (mdmSupplierGoodsDetail == null || checkRequiredParams(mdmSupplierGoodsDetail)) {
            log.info("【供应商商品消息同步】=======MDM商品详情无效，跳过处理");
            return;
        }

        if (mallSupplierGoodsDetail == null) {
            // 创建新商品
            handleGoodsCreate(message, mdmSupplierGoodsDetail);
        } else {
            // 更新现有商品
            handleGoodsUpdate(message, mdmSupplierGoodsDetail, mallSupplierGoodsDetail);
        }
    }

    /**
     * 处理商品创建
     */
    private void handleGoodsCreate(SupplierGoodsMessage message, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail) {
        ProductDTO productDTO = getProductIssueDetail(mdmSupplierGoodsDetail, message.getMallShopId());
        log.info("【供应商商品消息同步】=======发布新商品: mdmGoodsId={}, goodsName={}",
                productDTO.getMdmGoodsId(), productDTO.getName());
        editSupplierGoodsService.issueMdmSupplierProduct(productDTO);
    }

    /**
     * 处理商品更新
     */
    private void handleGoodsUpdate(SupplierGoodsMessage message, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, SupplierGoods mallSupplierGoodsDetail) {
        ProductDTO productDTO = getProductUpdateDetail(mdmSupplierGoodsDetail, message.getMallShopId(), mallSupplierGoodsDetail);
        log.info("【供应商商品消息同步】=======修改商品: mdmGoodsId={}, goodsName={}",
                productDTO.getMdmGoodsId(), productDTO.getName());
        editSupplierGoodsService.updateMdmSupplierProduct(productDTO, message.getSupplierBusinessType());
    }

    /**
     * 从mdm批量查询供应商商品详情
     */
    private List<ProviderStoreGoodsExtendChannelVO> getMdmSupplierGoodsDetailBatch(Long enterpriseId, Long mdmShopId, List<Long> productId) {
        SupplierGoodsDetailQueryReqDTO queryReqDTO = new SupplierGoodsDetailQueryReqDTO()
                .setEnterpriseId(enterpriseId)
                .setShopId(mdmShopId)
                .setProductIds(productId)
                .setChannelId(mdmConfig.getChannelId());
        return new SupplierGoodsDetailBatchQueryClient(ipaasClientSupport).execute(queryReqDTO);
    }

    /**
     * 处理商品状态变更
     */
    private void handleGoodsStatusChange(SupplierGoods mallSupplierGoodsDetail, ProductStatus status) {
        log.info("【供应商商品消息同步】=======商品状态变更: {}，{}", mallSupplierGoodsDetail.getId(), status.getDesc());
        editSupplierGoodsService.updateMdmSupplierProductStatus(
                Boolean.TRUE,
                mallSupplierGoodsDetail.getShopId(),
                new com.medusa.gruul.addon.supplier.model.dto.ProductStatusChangeDTO()
                        .setProductIds(Set.of(mallSupplierGoodsDetail.getId())),
                status
        );
    }

    /**
     * 转换商品发布详情
     */
    private ProductDTO getProductIssueDetail(ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, Long mallShopId) {
        GoodsChannelExternalVO goodsExternal = mdmSupplierGoodsDetail.getGoodsChannelExternal();
        ProductDTO productDTO = new ProductDTO();

        // 设置基本信息
        setBasicInfo(mallShopId, productDTO, mdmSupplierGoodsDetail, goodsExternal);

        // 设置商品类型和状态
        setProductTypeAndStatus(productDTO);

        // 设置配送方式
        setDistributionMode(productDTO, goodsExternal);

        // 设置运费模板和服务保障
        setFreightAndService(productDTO, goodsExternal);

        // 设置分类信息
        setCategoryInfo(productDTO, mdmSupplierGoodsDetail);

        // 设置SKU信息
        setSkuInfo(mallShopId, productDTO, mdmSupplierGoodsDetail, true);

        return productDTO;
    }

    /**
     * 转换商品更新详情
     */
    private ProductDTO getProductUpdateDetail(ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, Long mallShopId, SupplierGoods mallSupplierGoodsDetail) {
        GoodsChannelExternalVO goodsExternal = mdmSupplierGoodsDetail.getGoodsChannelExternal();
        ProductDTO productDTO = new ProductDTO();

        // 设置基本信息
        productDTO.setId(mallSupplierGoodsDetail.getId());
        setBasicInfo(mallShopId, productDTO, mdmSupplierGoodsDetail, goodsExternal);

        // 设置商品类型和状态
        setProductTypeAndStatus(productDTO);

        // 设置配送方式
        setDistributionMode(productDTO, goodsExternal);

        // 设置运费模板和服务保障
        setFreightAndService(productDTO, goodsExternal);

        // 设置分类信息
        setCategoryInfo(productDTO, mdmSupplierGoodsDetail);

        // 设置SKU信息
        setSkuInfo(mallShopId, productDTO, mdmSupplierGoodsDetail, false);

        return productDTO;
    }

    /**
     * 校验必填参数
     */
    private boolean checkRequiredParams(ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail) {
        // 校验商品名称
        if (StrUtil.isEmpty(mdmSupplierGoodsDetail.getGoodsName())) {
            log.info("【供应商商品消息同步】=======商品名称为空，mdmGoodsId：{}", mdmSupplierGoodsDetail.getId());
            return true;
        }

        if (!Objects.equals(GoodsValuationMethod.normal.getCode(), mdmSupplierGoodsDetail.getValuationMethod())) {
            log.info("【供应商商品消息同步】=======商品计价状态不是普通，mdmGoodsId：{}", mdmSupplierGoodsDetail.getId());
            return true;
        }

        // 校验店铺类目，只上架挂在3及分类的商品
        if (mdmSupplierGoodsDetail.getCategoryPlatforms() == null || mdmSupplierGoodsDetail.getCategoryPlatforms().getThree() == null) {
            log.info("【供应商商品消息同步】=======商品未挂载3级平台类目，mdmGoodsId：{}，goodsName{}", mdmSupplierGoodsDetail.getId(), mdmSupplierGoodsDetail.getGoodsName());
            return true;
        }

        // 校验sku
        if (CollUtil.isEmpty(mdmSupplierGoodsDetail.getStoreGoodsSpecDetailsList())) {
            log.info("【供应商商品消息同步】=======商品sku为空，mdmGoodsId：{}，goodsName{}", mdmSupplierGoodsDetail.getId(), mdmSupplierGoodsDetail.getGoodsName());
            return true;
        }
        return false;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(Long mallShopId, ProductDTO productDTO, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, GoodsChannelExternalVO goodsExternal) {
        productDTO.setMdmGoodsId(mdmSupplierGoodsDetail.getId());
        productDTO.setShopId(mallShopId);
        productDTO.setName(mdmSupplierGoodsDetail.getGoodsName());
        productDTO.setSaleDescribe(Optional.ofNullable(goodsExternal.getGoodsDesc()).orElse(""));

        // 设置标签
        Optional.ofNullable(mdmSupplierGoodsDetail.getGoodsLabel()).ifPresent(productDTO::setLabelId);

        // 设置视频
        Optional.ofNullable(goodsExternal.getVideo()).ifPresent(video -> productDTO.setVideoUrl(video.getUrl()));

        // 设置图片
        setPics(productDTO, mdmSupplierGoodsDetail.getPicture(), mdmSupplierGoodsDetail.getOtherPictures());
    }

    /**
     * 设置图片
     */
    private void setPics(ProductDTO productDTO, List<GoodsPictureVO> coverPictures, Map<String, List<GoodsPictureVO>> otherPictures) {
        // 处理画册图片
        List<String> albumPics = new ArrayList<>();

        // 添加商品封面
        boolean hasCoverPictures = CollUtil.isNotEmpty(coverPictures);
        if (hasCoverPictures) {
            albumPics.addAll(coverPictures.stream().map(GoodsPictureVO::getUrl).toList());
        }

        // 处理otherPictures
        boolean hasOtherPictures = CollUtil.isNotEmpty(otherPictures);
        if (hasOtherPictures) {
            // key=0: albumPics
            List<GoodsPictureVO> albumList = otherPictures.get("0");
            boolean hasAlbumList = CollUtil.isNotEmpty(albumList);
            if (hasAlbumList) {
                albumPics.addAll(albumList.stream().map(GoodsPictureVO::getUrl).toList());
            }

            // key=1: detail images
            List<GoodsPictureVO> detailList = otherPictures.get("1");
            boolean hasDetailList = CollUtil.isNotEmpty(detailList);
            if (hasDetailList) {
                // 保证有序
                List<String> detailImageUrls = detailList.stream()
                        .map(GoodsPictureVO::getUrl)
                        .distinct()
                        .collect(Collectors.toList());

                boolean hasDetailImages = !detailImageUrls.isEmpty();
                if (hasDetailImages) {
                    productDTO.setDetail(convertImagesToRichText(detailImageUrls));
                }
            }
        }

        // 设置画册图片，如果没有图片则设置默认图片
        boolean hasAlbumPics = !albumPics.isEmpty();
        if (!hasAlbumPics) {
            albumPics.add(DEFAULT_IMG_URL);
        }
        productDTO.setAlbumPics(String.join(",", albumPics));

        // 如果没有设置商品详情，则设置为空字符串
        boolean hasDetail = StrUtil.isNotEmpty(productDTO.getDetail());
        if (!hasDetail) {
            productDTO.setDetail("");
        }
    }

    /**
     * 设置商品类型和状态
     */
    private void setProductTypeAndStatus(ProductDTO productDTO) {
        productDTO.setProductType(ProductType.REAL_PRODUCT);
        productDTO.setStatus(ProductStatus.SELL_ON);
        productDTO.setSellType(SellType.CONSIGNMENT);
    }

    /**
     * 设置配送方式
     */
    private void setDistributionMode(ProductDTO productDTO, GoodsChannelExternalVO goodsExternal) {
        List<DistributionMode> distributionModes = new ArrayList<>();

        if (Boolean.TRUE.equals(goodsExternal.getIsCityDelivery())) {
            distributionModes.add(DistributionMode.INTRA_CITY_DISTRIBUTION);
        }
        if (Boolean.TRUE.equals(goodsExternal.getIsSelfPickup())) {
            distributionModes.add(DistributionMode.SHOP_STORE);
        }
        if (Boolean.TRUE.equals(goodsExternal.getIsExpressDelivery())) {
            distributionModes.add(DistributionMode.EXPRESS);
        }

        productDTO.setDistributionMode(distributionModes);
    }

    /**
     * 设置运费模板和服务保障
     */
    private void setFreightAndService(ProductDTO productDTO, GoodsChannelExternalVO goodsExternal) {
        // 设置运费模板
        productDTO.setFreightTemplateId(Optional.ofNullable(goodsExternal.getFreightTemplateId()).orElse(0L));

        // 设置服务保障
        Optional.ofNullable(goodsExternal.getServiceGuarantee()).filter(StrUtil::isNotEmpty).ifPresent(serviceGuarantee -> productDTO.setServiceIds(JSONUtil.parseArray(serviceGuarantee).stream().map(serviceBarrier -> ServiceBarrier.getByValue((Integer) serviceBarrier)).filter(Objects::nonNull).toList()));
    }

    /**
     * 设置分类信息
     */
    private void setCategoryInfo(ProductDTO productDTO, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail) {
        // 设置平台类目
        CategoryLevel platformCategory = new CategoryLevel();
        Optional.ofNullable(mdmSupplierGoodsDetail.getCategoryPlatforms()).ifPresent(categories -> {
            Optional.ofNullable(categories.getOne()).ifPresent(one -> platformCategory.setOne(one.getId()));
            Optional.ofNullable(categories.getTwo()).ifPresent(two -> platformCategory.setTwo(two.getId()));
            Optional.ofNullable(categories.getThree()).ifPresent(three -> platformCategory.setThree(three.getId()));
        });
        productDTO.setPlatformCategory(platformCategory);
    }

    /**
     * 设置SKU信息
     */
    private void setSkuInfo(Long mallShopId, ProductDTO productDTO, ProviderStoreGoodsExtendChannelVO mdmSupplierGoodsDetail, boolean isNew) {
        List<ProviderStoreGoodsSpecAndDetailsFullVO> specDetailsList = mdmSupplierGoodsDetail.getStoreGoodsSpecDetailsList();
        if (CollUtil.isEmpty(specDetailsList)) {
            return;
        }

        // 是多规格
        boolean isMultipleSpec = mdmSupplierGoodsDetail.getGoodsSpec() == CommonPool.NUMBER_TWO;

        // 处理规格组
        if (isMultipleSpec) {
            // 获取所有SKU的规格关系并合并
            List<StoreGoodsSpecRelationVO> allSpecRelations = specDetailsList.stream().flatMap(spec -> spec.getStoreGoodsSpecRelationList().stream()).toList();

            if (CollUtil.isNotEmpty(allSpecRelations)) {
                // 按规格组名称分组
                Map<String, List<StoreGoodsSpecRelationVO>> specGroupMap = allSpecRelations.stream().collect(Collectors.groupingBy(StoreGoodsSpecRelationVO::getGoodsSpecName));

                List<SpecGroupDTO> specGroups = new ArrayList<>();
                specGroupMap.forEach((specName, relations) -> {
                    if (CollUtil.isNotEmpty(relations)) {
                        SpecGroupDTO specGroup = new SpecGroupDTO();
                        // 设置规格组名称
                        specGroup.setName(specName);
                        // 设置规格组排序
                        specGroup.setOrder(relations.get(0).getGoodsSpecSort());

                        // 设置规格值列表（去重）
                        List<SpecDTO> children = relations.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StoreGoodsSpecRelationVO::getGoodsSpecDetailName))), set -> set.stream().map(relation -> {
                            SpecDTO spec = new SpecDTO();
                            spec.setName(relation.getGoodsSpecDetailName());
                            spec.setOrder(relation.getGoodsSpecDetailSort());
                            return spec;
                        }).toList()));
                        specGroup.setChildren(children);
                        specGroups.add(specGroup);
                    }
                });
                productDTO.setSpecGroups(specGroups);
            }
        }

        List<SkuDTO> skus = specDetailsList.stream().map(spec -> {
            SkuDTO sku = new SkuDTO();

            // 设置SKU图片
            Optional.ofNullable(spec.getImage()).ifPresent(image -> sku.setImage(image.getUrl()));

            // 设置SKU基本信息
            sku.setMdmSkuId(spec.getSkuId());
            //设置skuId
            if (!isNew) {
                sku.setId(storageRpcService.getSKuIdByMdmSkuId(spec.getSkuId(), mallShopId));
            }
            if (isNew) {
                //初始库存 仅新增sku时 可以使用
                sku.setInitStock(Optional.ofNullable(spec.getInventory()).orElse(0L));
            }
            // 设置初始销量
            sku.setInitSalesVolume(Optional.ofNullable(spec.getOnlineInitSales()).orElse(0L));
            // 设置限购类型
            Integer limitNum = Optional.ofNullable(spec.getLimitBuyNum()).orElse(0);
            if (limitNum > 0) {
                if (isMultipleSpec) {
                    sku.setLimitType(LimitType.SKU_LIMITED);
                } else {
                    sku.setLimitType(LimitType.PRODUCT_LIMITED);
                }
            } else {
                sku.setLimitType(LimitType.UNLIMITED);
            }

            if (!isMultipleSpec) {
                // 单规格得sku图片就是商品图片
                sku.setImage(productDTO.getPic());
            }

            // 设置限购数量
            sku.setLimitNum(limitNum);
            // 设置划线价
            sku.setPrice(spec.getLinePrice() == null ? 0L : AmountCalculateHelper.toMilli(spec.getLinePrice()));
            // 供应商需求：库存不限
            sku.setStockType(StockType.UNLIMITED);
            // 设置重量
            sku.setWeight(Optional.ofNullable(spec.getWeight()).orElse(new BigDecimal("0")));

            // 设置SKU价格
            sku.setSalePrice(spec.getSellingPrice() == null ? 0L : AmountCalculateHelper.toMilli(spec.getSellingPrice()));
            // 设置SKU规格值
            if (isMultipleSpec) {
                List<String> specs = spec.getStoreGoodsSpecRelationList().stream().map(StoreGoodsSpecRelationVO::getGoodsSpecDetailName).toList();
                sku.setSpecs(specs);
            }

            return sku;
        }).toList();
        productDTO.setSkus(skus);
    }

    /**
     * 将图片地址集合转换为富文本格式
     *
     * @param imageUrls 图片地址集合
     * @return 富文本格式的图片字符串
     */
    private String convertImagesToRichText(List<String> imageUrls) {
        if (CollUtil.isEmpty(imageUrls)) {
            return "";
        }

        StringBuilder richText = new StringBuilder("<p>");
        for (String imageUrl : imageUrls) {
            richText.append(String.format("<img src=\"%s\" alt=\"\" data-href=\"\" style=\"\"/>", imageUrl));
        }
        richText.append("</p>");

        return richText.toString();
    }
}
