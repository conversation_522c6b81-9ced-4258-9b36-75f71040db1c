package com.medusa.gruul.goods.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.common.ipaas.MdmConfig;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.goods.GoodsDetailBatchQueryClient;
import com.medusa.gruul.common.ipaas.client.team.StoreQueryByTeamInfoIdsClient;
import com.medusa.gruul.common.ipaas.model.goods.*;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsReqDTO;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsRspVO;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.ConsignmentPriceSettingDTO;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.dto.ProductStatusChangeDTO;
import com.medusa.gruul.goods.api.model.dto.SinglePaveGoodsDTO;
import com.medusa.gruul.goods.api.model.enums.PricingType;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.ProductType;
import com.medusa.gruul.goods.service.addon.GoodsAddonSupporter;
import com.medusa.gruul.goods.service.model.dto.ConsignmentProductDTO;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.mq.rocketmq.goods.GoodsBusinessType;
import com.medusa.gruul.goods.service.mq.rocketmq.goods.GoodsMessage;
import com.medusa.gruul.goods.service.mq.rocketmq.goods.GoodsValuationMethod;
import com.medusa.gruul.goods.service.service.ConsignmentService;
import com.medusa.gruul.goods.service.service.GoodsMessageService;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.dto.SkuDTO;
import com.medusa.gruul.storage.api.dto.SpecDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * mdm商品消息
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsMessageServiceImpl implements GoodsMessageService {
    /**
     * 批量处理商品的最大数量
     */
    private static final int BATCH_PROCESS_MAX_SIZE = 100;
    /**
     * 商品消息时间戳缓存过期时间（天）
     */
    private static final int GOODS_MESSAGE_TIMESTAMP_EXPIRE_DAYS = 7;
    /**
     * 商品消息时间戳缓存前缀
     */
    private static final String GOODS_MESSAGE_TIMESTAMP_PREFIX = "goods:message:timestamp:";
    /**
     * 默认商品图片
     */
    private static final String DEFAULT_IMG_URL = "https://m-oss.holderzone.cn/fsgen/15876910489a0c1543451be7130e7b53%E5%95%86%E5%93%81%E9%BB%98%E8%AE%A4%E5%9B%BE%E7%89%87.png";

    private final IpaasClientSupport ipaasClientSupport;
    private final MdmConfig mdmConfig;
    private final IProductService productService;
    private final ShopRpcService shopRpcService;
    private final StorageRpcService storageRpcService;
    private final ConsignmentService consignmentService;
    private final GoodsAddonSupporter goodsAddonSupporter;
    private final Executor goodsExecutor;

    @Override
    @Redisson(value = GoodsConstant.GOODS_MESSAGE_PROCESS_LOCK, key = "#message.enterpriseId + ':' + #message.mallShopId", waitTime = 30, leaseTime = 300)
    public void processGoodsMessage(GoodsMessage message) {
        // 1. 验证消息
        if (!validateMessage(message)) {
            return;
        }

        // 2. 获取店铺信息
        ShopInfoVO shopInfo = getShopInfo(message.getMallShopId());
        if (shopInfo == null) {
            return;
        }

        // 3. 设置系统上下文
        setSystemContext(message.getEnterpriseId(), shopInfo);

        // 4. 处理商品消息
        List<Long> mdmGoodsIds = message.getGoodsId();
        if (CollUtil.isEmpty(mdmGoodsIds)) {
            log.info("【商品消息同步】=======商品ID列表为空");
            return;
        }

        // 获取消息时间戳
        Long messageTimestamp = message.getTimestamp();

        // 过滤需要处理的商品（基于时间戳）
        List<Long> filteredGoodsIds = mdmGoodsIds.stream()
                .filter(mdmGoodsId -> shouldProcessGoodsMessage(mdmGoodsId, messageTimestamp))
                .toList();
        if (CollUtil.isEmpty(filteredGoodsIds)) {
            log.info("【商品消息同步】=======所有商品消息都已过期，跳过处理");
            return;
        }

        // 分批处理商品
        int totalSize = filteredGoodsIds.size();
        log.info("【商品消息同步】=======开始处理商品，总数: {}", totalSize);

        // 使用线程池并行处理批次
        List<CompletableFuture<Void>> batchFutures = new ArrayList<>();
        for (int i = 0; i < totalSize; i += BATCH_PROCESS_MAX_SIZE) {
            int endIndex = Math.min(i + BATCH_PROCESS_MAX_SIZE, totalSize);
            List<Long> batchGoodsIds = filteredGoodsIds.subList(i, endIndex);

            CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() -> {
                processGoodsBatch(message, batchGoodsIds, messageTimestamp);
            }, goodsExecutor);

            batchFutures.add(batchFuture);
        }

        // 等待所有批次处理完成
        CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).join();

        log.info("【商品消息同步】=======商品处理完成，总数: {}", totalSize);
    }

    /**
     * 验证消息
     */
    private boolean validateMessage(GoodsMessage message) {
        if (message.getMallShopId() == null
                || message.getStoreId() == null
                || message.getEnterpriseId() == null
                || !message.getChannelId().contains(mdmConfig.getChannelId())) {
            log.info("【商品消息同步】=======消息参数无效: mallShopId/mdmShopId/enterpriseId为空，或不是商城渠道商品");
            return false;
        }

        GoodsBusinessType businessType = GoodsBusinessType.fromCode(message.getBusinessType());
        if (businessType == null) {
            log.info("【商品消息同步】=======无效的业务类型: businessType={}", message.getBusinessType());
            return false;
        }
        log.info("【商品消息同步】=======业务类型: businessType={},{}", businessType.getCode(), businessType.getDesc());

        return true;
    }

    /**
     * 获取店铺信息
     */
    private ShopInfoVO getShopInfo(Long mallShopId) {
        ShopInfoVO shopInfo = shopRpcService.getShopInfoByShopId(mallShopId);
        if (shopInfo == null) {
            log.info("【商品消息同步】=======商城店铺不存在");
            return null;
        }
        return shopInfo;
    }

    /**
     * 设置系统上下文
     */
    private void setSystemContext(Long enterpriseId, ShopInfoVO shopInfo) {
        SystemContextHolder.set(new Systems()
                .setEnterpriseId(enterpriseId)
                .setPlatformId(shopInfo.getPlatformId())
                .setShopId(shopInfo.getId()));
    }

    /**
     * 处理商品批次
     */
    private void processGoodsBatch(GoodsMessage message, List<Long> batchGoodsIds, Long messageTimestamp) {
        // 1. 批量查询本地商品信息
        Map<Long, Product> productMap = getProductMap(batchGoodsIds);

        // 2. 批量查询MDM商品详情
        List<StoreGoodsExtendChannelVO> mdmGoodsDetails = getMdmGoodsDetailBatch(
                message.getEnterpriseId(),
                message.getStoreId(),
                batchGoodsIds
        );

        // 过滤出供应商店铺ID
        List<Long> providerStoreIds = mdmGoodsDetails.stream()
                .map(StoreGoodsExtendChannelVO::getProviderStoreId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 构建供应商映射：StoreQueryByTeamInfoIdsRspVO.storeTeamInfoId -> Shop.id
        Map<Long, Long> supplierMap = getSupplierMap(providerStoreIds);

        // 3. 构建MDM商品Map
        Map<Long, StoreGoodsExtendChannelVO> mdmGoodsMap = CollUtil.isEmpty(mdmGoodsDetails) ?
                new HashMap<>() :
                mdmGoodsDetails.stream().collect(Collectors.toMap(StoreGoodsExtendChannelVO::getId, goods -> goods, (existing, replacement) -> existing));

        // 4. 并行处理每个商品
        List<CompletableFuture<Void>> goodsFutures = batchGoodsIds.stream()
                .map(mdmGoodsId -> CompletableFuture.runAsync(() -> {
                    processSingleGoods(message, mdmGoodsMap.get(mdmGoodsId), productMap.get(mdmGoodsId), supplierMap);
                    // 更新商品消息时间戳缓存
                    updateGoodsMessageTimestamp(mdmGoodsId, messageTimestamp);
                }, goodsExecutor))
                .toList();

        // 等待所有商品处理完成
        CompletableFuture.allOf(goodsFutures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 获取供应商店铺信息
     *
     * @param providerStoreIds 供应商店铺ID列表
     * @return StoreQueryByTeamInfoIdsRspVO.storeTeamInfoId -> Shop.id
     */
    private Map<Long, Long> getSupplierMap(List<Long> providerStoreIds) {
        Map<Long, Long> supplierMap = new HashMap<>();
        if (CollUtil.isEmpty(providerStoreIds)) {
            return supplierMap;
        }

        List<StoreQueryByTeamInfoIdsRspVO> providerStores = new StoreQueryByTeamInfoIdsClient(ipaasClientSupport)
                .execute(new StoreQueryByTeamInfoIdsReqDTO().setTeamInfoIds(providerStoreIds));
        if (CollUtil.isEmpty(providerStores)) {
            return supplierMap;
        }

        // 获取所有需要的店铺信息
        List<Long> storeIds = providerStores.stream()
                .map(StoreQueryByTeamInfoIdsRspVO::getId)
                .toList();
        List<Shop> supplierList = shopRpcService.listShopsByIpaasStoreIds(storeIds);
        if (CollUtil.isEmpty(supplierList)) {
            return supplierMap;
        }

        // 构建店铺映射
        Map<Long, Shop> shopMap = supplierList.stream()
                .collect(Collectors.toMap(Shop::getIpaasStoreId, Function.identity()));

        // 构建最终映射，过滤掉空值
        supplierMap = providerStores.stream()
                .filter(providerStore -> shopMap.containsKey(providerStore.getId()))
                .collect(Collectors.toMap(
                        StoreQueryByTeamInfoIdsRspVO::getStoreTeamInfoId,
                        providerStore -> shopMap.get(providerStore.getId()).getId()
                ));

        return supplierMap;
    }

    /**
     * 检查是否应该处理该商品消息
     *
     * @param mdmGoodsId       商品ID
     * @param messageTimestamp 消息时间戳
     * @return true: 需要处理; false: 不需要处理（消息已过期）
     */
    private boolean shouldProcessGoodsMessage(Long mdmGoodsId, Long messageTimestamp) {
        String redisKey = getGoodsMessageTimestampKey(mdmGoodsId);
        Long lastTimestamp = RedisUtil.getCacheObject(redisKey);

        // 如果Redis中没有该商品的时间戳记录，或者当前消息时间戳大于上次处理的时间戳，则处理该消息
        return lastTimestamp == null || messageTimestamp > lastTimestamp;
    }

    /**
     * 更新商品消息时间戳缓存
     *
     * @param mdmGoodsId       商品ID
     * @param messageTimestamp 消息时间戳
     */
    private void updateGoodsMessageTimestamp(Long mdmGoodsId, Long messageTimestamp) {
        String redisKey = getGoodsMessageTimestampKey(mdmGoodsId);
        Long lastTimestamp = RedisUtil.getCacheObject(redisKey);

        // 双重检查，确保时间戳是最新的
        if (lastTimestamp == null || messageTimestamp > lastTimestamp) {
            RedisUtil.setCacheObject(redisKey, messageTimestamp, GOODS_MESSAGE_TIMESTAMP_EXPIRE_DAYS, TimeUnit.DAYS);
        }
    }

    /**
     * 获取商品消息时间戳的Redis键
     *
     * @param mdmGoodsId 商品ID
     * @return Redis键
     */
    private String getGoodsMessageTimestampKey(Long mdmGoodsId) {
        return GOODS_MESSAGE_TIMESTAMP_PREFIX + mdmGoodsId;
    }

    /**
     * 获取商品Map
     */
    private Map<Long, Product> getProductMap(List<Long> mdmGoodsIds) {
        return productService.lambdaQuery()
                .in(Product::getMdmGoodsId, mdmGoodsIds)
                .list()
                .stream()
                .collect(Collectors.toMap(Product::getMdmGoodsId, product -> product));
    }

    /**
     * 处理单个商品
     */
    private void processSingleGoods(GoodsMessage message, StoreGoodsExtendChannelVO mdmGoodsDetail, Product mallProductDetail, Map<Long, Long> supplierMap) {
        if (GoodsBusinessType.GOODS_DELETE.getCode().equals(message.getBusinessType())) {
            deleteSingleGoods(mallProductDetail);
        } else {
            updateSingleGoods(message, mdmGoodsDetail, mallProductDetail, supplierMap);
        }
    }

    /**
     * 删除单个商品
     */
    private void deleteSingleGoods(Product mallProductDetail) {
        if (mallProductDetail == null) {
            return;
        }

        handleSellOff(mallProductDetail);
        handleMdmGoodsDelete(mallProductDetail.getShopId(), mallProductDetail.getId());
    }

    /**
     * 更新单个商品
     */
    private void updateSingleGoods(GoodsMessage message, StoreGoodsExtendChannelVO mdmGoodsDetail,
                                   Product mallProductDetail, Map<Long, Long> supplierMap) {
        if (mallProductDetail == null && mdmGoodsDetail == null) {
            return;
        }

        // 是代销商品
        if ((mallProductDetail != null && mallProductDetail.getSupplierId() != null) ||
                (mdmGoodsDetail != null && mdmGoodsDetail.getProviderStoreId() != null)) {
            updateSingleSupplierGoods(message, mdmGoodsDetail, mallProductDetail, supplierMap);
        } else {
            // 普通商品
            updateSingleNomalGoods(message, mdmGoodsDetail, mallProductDetail);
        }
    }

    /**
     * 更新单个普通商品
     */
    private void updateSingleNomalGoods(GoodsMessage message, StoreGoodsExtendChannelVO mdmGoodsDetail, Product mallProductDetail) {
        if (mallProductDetail == null) {
            // 新商品：检查MDM商品详情是否存在且有效
            if (mdmGoodsDetail == null || checkRequiredParams(mdmGoodsDetail)) {
                return;
            }

            // 处理商品发布
            ProductDTO productIssueDetail = getProductIssueDetail(mdmGoodsDetail, message.getMallShopId());
            handleMdmGoodsIssue(productIssueDetail);
        } else {
            // 已存在商品：检查MDM商品详情
            if (mdmGoodsDetail == null || checkRequiredParams(mdmGoodsDetail)) {
                // MDM中不存在或无效，执行下架
                handleSellOff(mallProductDetail);
            } else {
                // MDM中存在且有效，执行更新
                ProductDTO productUpdateDetail = getProductUpdateDetail(mdmGoodsDetail, message.getMallShopId(), mallProductDetail);
                handleSellOnAndUpdate(mallProductDetail, productUpdateDetail);
            }
        }
    }

    /**
     * 更新单个供应商商品
     */
    private void updateSingleSupplierGoods(GoodsMessage message, StoreGoodsExtendChannelVO mdmGoodsDetail, Product mallProductDetail, Map<Long, Long> supplierMap) {
        if (mallProductDetail == null) {
            // 新商品：检查MDM商品详情是否存在且有效
            if (mdmGoodsDetail == null || checkRequiredParams(mdmGoodsDetail)) {
                return;
            }

            // 铺货
            Long supplierId = supplierMap.get(mdmGoodsDetail.getProviderStoreId());
            if (supplierId == null) {
                return;
            }
            handlePaveGoods(message.getMallShopId(), supplierId, mdmGoodsDetail);
        } else {
            // 已存在商品：检查MDM商品详情
            if (mdmGoodsDetail == null || checkRequiredParams(mdmGoodsDetail)) {
                // MDM中不存在或无效，执行下架
                handleSellOff(mallProductDetail);
            } else {
                // MDM中存在且有效，执行更新
                handleSupplierGoodsSellOnAndUpdate(mallProductDetail, mdmGoodsDetail);
            }
        }
    }

    /**
     * 处理代销商品更新
     */
    private void handleConsignmentProductUpdate(StoreGoodsExtendChannelVO mdmGoodsDetail, Product mallProductDetail) {
        ConsignmentProductDTO consignmentProduct = new ConsignmentProductDTO()
                .setId(mallProductDetail.getId())
                .setConsignmentPriceSetting(getConsignmentPriceSetting(mdmGoodsDetail))
                .setName(mdmGoodsDetail.getGoodsName())
                .setShopCategory(getShopCategory(mdmGoodsDetail.getSaleCategories()))
                .setSaleDescribe(Optional.ofNullable(mdmGoodsDetail.getGoodsChannelExternal().getGoodsDesc()).orElse(""))
                .setLabelId(mdmGoodsDetail.getGoodsLabel());

        // 处理画册图片
        List<String> albumPics = new ArrayList<>();

        List<GoodsPictureVO> coverPictures = mdmGoodsDetail.getPicture();
        Map<String, List<GoodsPictureVO>> otherPictures = mdmGoodsDetail.getOtherPictures();

        // 添加商品封面
        boolean hasCoverPictures = CollUtil.isNotEmpty(coverPictures);
        if (hasCoverPictures) {
            albumPics.addAll(coverPictures.stream().map(GoodsPictureVO::getUrl).toList());
        }

        // 处理otherPictures
        boolean hasOtherPictures = CollUtil.isNotEmpty(otherPictures);
        if (hasOtherPictures) {
            // key=0: albumPics
            List<GoodsPictureVO> albumList = otherPictures.get("0");
            boolean hasAlbumList = CollUtil.isNotEmpty(albumList);
            if (hasAlbumList) {
                albumPics.addAll(albumList.stream().map(GoodsPictureVO::getUrl).toList());
            }

        }

        // 设置画册图片，如果没有图片则设置默认图片
        boolean hasAlbumPics = !albumPics.isEmpty();
        if (!hasAlbumPics) {
            albumPics.add(DEFAULT_IMG_URL);
        }
        consignmentProduct.setPic(albumPics.get(CommonPool.NUMBER_ZERO));
        consignmentProduct.setAlbumPics(String.join(",", albumPics));

        productService.consignmentMdmProductUpdate(mallProductDetail.getShopId(), consignmentProduct);
    }

    /**
     * 处理铺货
     */
    private void handlePaveGoods(Long mallShopId, Long supplierId, StoreGoodsExtendChannelVO mdmGoodsDetail) {
        Long supplierGoodsId = goodsAddonSupporter.getSupplierGoodsId(supplierId, mdmGoodsDetail.getProviderGoodsId());
        if (supplierGoodsId == null) {
            return;
        }
        ShopProductKey shopProductKey = new ShopProductKey()
                .setShopId(supplierId)
                .setProductId(supplierGoodsId);

        SinglePaveGoodsDTO singlePaveGoodsDTO = new SinglePaveGoodsDTO()
                .setShopProductKey(shopProductKey)
                .setConsignmentPriceSetting(getConsignmentPriceSetting(mdmGoodsDetail))
                .setShopCategory(getShopCategory(mdmGoodsDetail.getSaleCategories()))
                .setName(mdmGoodsDetail.getGoodsName())
                .setSaleDescribe(Optional.ofNullable(mdmGoodsDetail.getGoodsChannelExternal().getGoodsDesc()).orElse(""))
                .setLabelId(mdmGoodsDetail.getGoodsLabel());

        consignmentService.singlePaveMdmGoods(mallShopId, mdmGoodsDetail.getId(), singlePaveGoodsDTO);
        log.info("【商品消息同步】=======铺货新商品: mdmGoodsId={}，goodsName={}", mdmGoodsDetail.getId(), mdmGoodsDetail.getGoodsName());
    }

    /**
     * 获取代销价格设置
     */
    private ConsignmentPriceSettingDTO getConsignmentPriceSetting(StoreGoodsExtendChannelVO mdmGoodsDetail) {
        return new ConsignmentPriceSettingDTO()
                .setType(mdmGoodsDetail.getProviderPriceSetting() == CommonPool.NUMBER_ONE ? PricingType.RATE : PricingType.REGULAR)
                .setSale(mdmGoodsDetail.getSellingPriceSeting() == null ? 0L : AmountCalculateHelper.toMilli(mdmGoodsDetail.getSellingPriceSeting()))
                .setScribe(mdmGoodsDetail.getLinePriceSeting() == null ? 0L : AmountCalculateHelper.toMilli(mdmGoodsDetail.getLinePriceSeting()));
    }

    /**
     * 获取店铺分类
     */
    private CategoryLevel getShopCategory(StoreGoodsExtendChannelVO.CategoryLevelVO mdmGoodsDetail) {
        CategoryLevel shopCategory = new CategoryLevel();
        Optional.ofNullable(mdmGoodsDetail).ifPresent(categories -> {
            Optional.ofNullable(categories.getOne()).ifPresent(one -> shopCategory.setOne(one.getId()));
            Optional.ofNullable(categories.getTwo()).ifPresent(two -> shopCategory.setTwo(two.getId()));
            Optional.ofNullable(categories.getThree()).ifPresent(three -> shopCategory.setThree(three.getId()));
        });
        return shopCategory;
    }

    /**
     * 处理下架
     */
    private void handleSellOff(Product product) {
        if (ProductStatus.SELL_ON == product.getStatus()) {
            handleMdmGoodsStatusChange(product.getShopId(), product.getId(), ProductStatus.SELL_OFF);
        }
    }

    /**
     * 处理上架和更新
     */
    private void handleSellOnAndUpdate(Product product, ProductDTO productUpdateDetail) {
        if (ProductStatus.SELL_OFF == product.getStatus()) {
            handleMdmGoodsStatusChange(product.getShopId(), product.getId(), ProductStatus.SELL_ON);
        }
        handleMdmGoodsUpdate(productUpdateDetail);
    }

    /**
     * 处理供应商商品上架和更新
     */
    private void handleSupplierGoodsSellOnAndUpdate(Product product, StoreGoodsExtendChannelVO mdmGoodsDetail) {
        if (ProductStatus.SELL_OFF == product.getStatus()) {
            handleMdmGoodsStatusChange(product.getShopId(), product.getId(), ProductStatus.SELL_ON);
        }
        handleConsignmentProductUpdate(mdmGoodsDetail, product);
    }

    /**
     * 从mdm批量查询商品详情
     */
    private List<StoreGoodsExtendChannelVO> getMdmGoodsDetailBatch(Long enterpriseId, Long mdmShopId, List<Long> productId) {
        GoodsDetailQueryReqDTO queryReqDTO = new GoodsDetailQueryReqDTO()
                .setEnterpriseId(enterpriseId)
                .setShopId(mdmShopId)
                .setProductIds(productId)
                .setIsQueryInventory(Boolean.TRUE)
                .setChannelId(mdmConfig.getChannelId());
        return new GoodsDetailBatchQueryClient(ipaasClientSupport).execute(queryReqDTO);
    }

    /**
     * 处理商品发布
     */
    private void handleMdmGoodsIssue(ProductDTO productDTO) {
        log.info("【商品消息同步】=======发布新商品: mdmGoodsId={}，goodsName={}", productDTO.getMdmGoodsId(), productDTO.getName());
        productService.issueMdmGoods(productDTO);
    }

    /**
     * 处理商品修改
     */
    private void handleMdmGoodsUpdate(ProductDTO productDTO) {
        log.info("【商品消息同步】=======修改商品: mdmGoodsId={}，goodsName={}", productDTO.getMdmGoodsId(), productDTO.getName());
        productService.updateSaleProduct(productDTO);
    }

    /**
     * 处理商品状态变更
     */
    private void handleMdmGoodsStatusChange(Long shopId, Long productId, ProductStatus status) {
        log.info("【商品消息同步】=======商品状态变更: {}，{}", productId, status.getDesc());
        ProductStatusChangeDTO statusChangeDTO = new ProductStatusChangeDTO();
        statusChangeDTO.setKeys(Set.of(new ShopProductKey().setShopId(shopId).setProductId(productId)));
        productService.updateProductStatus(false, statusChangeDTO, status);
    }

    /**
     * 处理商品删除
     */
    private void handleMdmGoodsDelete(Long shopId, Long productId) {
        log.info("【商品消息同步】=======删除商品: {}", productId);
        productService.deleteMdmGoods(shopId, Set.of(productId));
    }

    /**
     * 转换商品发布详情
     */
    private ProductDTO getProductIssueDetail(StoreGoodsExtendChannelVO mdmGoodsDetail, Long mallShopId) {
        GoodsChannelExternalVO goodsExternal = mdmGoodsDetail.getGoodsChannelExternal();
        ProductDTO productDTO = new ProductDTO();

        // 设置基本信息
        setBasicInfo(mallShopId, productDTO, mdmGoodsDetail, goodsExternal);

        // 设置商品类型和状态
        setProductTypeAndStatus(productDTO);

        // 设置配送方式
        setDistributionMode(productDTO, goodsExternal);

        // 设置运费模板和服务保障
        setFreightAndService(productDTO, goodsExternal);

        // 设置分类信息
        setCategoryInfo(productDTO, mdmGoodsDetail);

        // 设置SKU信息
        setSkuInfo(mallShopId, productDTO, mdmGoodsDetail, true);

        return productDTO;
    }

    /**
     * 转换商品更新详情
     */
    private ProductDTO getProductUpdateDetail(StoreGoodsExtendChannelVO mdmGoodsDetail, Long mallShopId, Product product) {
        GoodsChannelExternalVO goodsExternal = mdmGoodsDetail.getGoodsChannelExternal();
        ProductDTO productDTO = new ProductDTO();

        // 设置基本信息
        productDTO.setId(product.getId());
        setBasicInfo(mallShopId, productDTO, mdmGoodsDetail, goodsExternal);

        // 设置商品类型和状态
        setProductTypeAndStatus(productDTO);

        // 设置配送方式
        setDistributionMode(productDTO, goodsExternal);

        // 设置运费模板和服务保障
        setFreightAndService(productDTO, goodsExternal);

        // 设置分类信息
        setCategoryInfo(productDTO, mdmGoodsDetail);

        // 设置SKU信息
        setSkuInfo(mallShopId, productDTO, mdmGoodsDetail, false);

        return productDTO;
    }

    /**
     * 校验必填参数
     */
    private boolean checkRequiredParams(StoreGoodsExtendChannelVO mdmGoodsDetail) {
        // 校验商品名称
        if (StrUtil.isEmpty(mdmGoodsDetail.getGoodsName())) {
            log.info("【商品消息同步】=======商品名称为空，mdmGoodsId：{}", mdmGoodsDetail.getId());
            return true;
        }

        if (!Objects.equals(GoodsValuationMethod.normal.getCode(), mdmGoodsDetail.getValuationMethod())) {
            log.info("【商品消息同步】=======商品计价状态不是普通，mdmGoodsId：{}", mdmGoodsDetail.getId());
            return true;
        }

        // 校验店铺类目，只上架挂在3及分类的商品
        if (mdmGoodsDetail.getCategoryPlatforms() == null || mdmGoodsDetail.getCategoryPlatforms().getThree() == null) {
            log.info("【商品消息同步】=======商品未挂载3级平台类目，mdmGoodsId：{}，goodsName{}", mdmGoodsDetail.getId(), mdmGoodsDetail.getGoodsName());
            return true;
        }

        // 校验店铺类目，只上架挂在3及分类的商品
        if (mdmGoodsDetail.getSaleCategories() == null || mdmGoodsDetail.getSaleCategories().getThree() == null) {
            log.info("【商品消息同步】=======商品未挂载3级店铺类目，mdmGoodsId：{}，goodsName{}", mdmGoodsDetail.getId(), mdmGoodsDetail.getGoodsName());
            return true;
        }

        // 校验sku
        if (CollUtil.isEmpty(mdmGoodsDetail.getStoreGoodsSpecDetailsList())) {
            log.info("【商品消息同步】=======商品sku为空，mdmGoodsId：{}，goodsName{}", mdmGoodsDetail.getId(), mdmGoodsDetail.getGoodsName());
            return true;
        }
        return false;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(Long mallShopId, ProductDTO productDTO, StoreGoodsExtendChannelVO mdmGoodsDetail, GoodsChannelExternalVO goodsExternal) {
        productDTO.setMdmGoodsId(mdmGoodsDetail.getId());
        productDTO.setShopId(mallShopId);
        productDTO.setName(mdmGoodsDetail.getGoodsName());
        productDTO.setSaleDescribe(Optional.ofNullable(goodsExternal.getGoodsDesc()).orElse(""));

        // 设置标签
        Optional.ofNullable(mdmGoodsDetail.getGoodsLabel()).ifPresent(productDTO::setLabelId);

        // 设置视频
        Optional.ofNullable(goodsExternal.getVideo()).ifPresent(video -> productDTO.setVideoUrl(video.getUrl()));

        // 设置图片
        setPics(productDTO, mdmGoodsDetail.getPicture(), mdmGoodsDetail.getOtherPictures());
    }

    /**
     * 设置图片
     */
    private void setPics(ProductDTO productDTO, List<GoodsPictureVO> coverPictures, Map<String, List<GoodsPictureVO>> otherPictures) {
        // 处理画册图片
        List<String> albumPics = new ArrayList<>();

        // 添加商品封面
        boolean hasCoverPictures = CollUtil.isNotEmpty(coverPictures);
        if (hasCoverPictures) {
            albumPics.addAll(coverPictures.stream().map(GoodsPictureVO::getUrl).toList());
        }

        // 处理otherPictures
        boolean hasOtherPictures = CollUtil.isNotEmpty(otherPictures);
        if (hasOtherPictures) {
            // key=0: albumPics
            List<GoodsPictureVO> albumList = otherPictures.get("0");
            boolean hasAlbumList = CollUtil.isNotEmpty(albumList);
            if (hasAlbumList) {
                albumPics.addAll(albumList.stream().map(GoodsPictureVO::getUrl).toList());
            }

            // key=1: detail images
            List<GoodsPictureVO> detailList = otherPictures.get("1");
            boolean hasDetailList = CollUtil.isNotEmpty(detailList);
            if (hasDetailList) {
                // 保证有序
                List<String> detailImageUrls = detailList.stream()
                        .map(GoodsPictureVO::getUrl)
                        .distinct()
                        .collect(Collectors.toList());

                boolean hasDetailImages = !detailImageUrls.isEmpty();
                if (hasDetailImages) {
                    productDTO.setDetail(convertImagesToRichText(detailImageUrls));
                }
            }
        }

        // 设置画册图片，如果没有图片则设置默认图片
        boolean hasAlbumPics = !albumPics.isEmpty();
        if (!hasAlbumPics) {
            albumPics.add(DEFAULT_IMG_URL);
        }
        productDTO.setAlbumPics(String.join(",", albumPics));

        // 如果没有设置商品详情，则设置为空字符串
        boolean hasDetail = StrUtil.isNotEmpty(productDTO.getDetail());
        if (!hasDetail) {
            productDTO.setDetail("");
        }
    }

    /**
     * 设置商品类型和状态
     */
    private void setProductTypeAndStatus(ProductDTO productDTO) {
        productDTO.setProductType(ProductType.REAL_PRODUCT);
        productDTO.setStatus(ProductStatus.SELL_ON);
        productDTO.setSellType(SellType.OWN);
    }

    /**
     * 设置配送方式
     */
    private void setDistributionMode(ProductDTO productDTO, GoodsChannelExternalVO goodsExternal) {
        List<DistributionMode> distributionModes = new ArrayList<>();

        if (Boolean.TRUE.equals(goodsExternal.getIsCityDelivery())) {
            distributionModes.add(DistributionMode.INTRA_CITY_DISTRIBUTION);
        }
        if (Boolean.TRUE.equals(goodsExternal.getIsSelfPickup())) {
            distributionModes.add(DistributionMode.SHOP_STORE);
        }
        if (Boolean.TRUE.equals(goodsExternal.getIsExpressDelivery())) {
            distributionModes.add(DistributionMode.EXPRESS);
        }

        productDTO.setDistributionMode(distributionModes);
    }

    /**
     * 设置运费模板和服务保障
     */
    private void setFreightAndService(ProductDTO productDTO, GoodsChannelExternalVO goodsExternal) {
        // 设置运费模板
        productDTO.setFreightTemplateId(Optional.ofNullable(goodsExternal.getFreightTemplateId()).orElse(0L));

        // 设置服务保障
        Optional.ofNullable(goodsExternal.getServiceGuarantee())
                .filter(StrUtil::isNotEmpty)
                .ifPresent(serviceGuarantee -> productDTO.setServiceIds(JSONUtil.parseArray(serviceGuarantee).stream().map(serviceBarrier -> ServiceBarrier.getByValue((Integer) serviceBarrier)).filter(Objects::nonNull).sorted(Comparator.comparing(ServiceBarrier::getValue)).toList()));
    }

    /**
     * 设置分类信息
     */
    private void setCategoryInfo(ProductDTO productDTO, StoreGoodsExtendChannelVO mdmGoodsDetail) {
        // 设置平台类目
        CategoryLevel platformCategory = getShopCategory(mdmGoodsDetail.getCategoryPlatforms());
        productDTO.setPlatformCategory(platformCategory);

        // 设置店铺类目
        CategoryLevel shopCategory = getShopCategory(mdmGoodsDetail.getSaleCategories());
        productDTO.setShopCategory(shopCategory);
    }

    /**
     * 设置SKU信息
     */
    private void setSkuInfo(Long mallShopId, ProductDTO productDTO, StoreGoodsExtendChannelVO mdmGoodsDetail, boolean isNew) {
        List<StoreGoodsSpecAndDetailsFullVO> specDetailsList = mdmGoodsDetail.getStoreGoodsSpecDetailsList();
        if (CollUtil.isEmpty(specDetailsList)) {
            return;
        }

        // 是多规格
        boolean isMultipleSpec = mdmGoodsDetail.getGoodsSpec() == CommonPool.NUMBER_TWO;

        // 处理规格组
        if (isMultipleSpec) {
            // 获取所有SKU的规格关系并合并
            List<StoreGoodsSpecRelationVO> allSpecRelations = specDetailsList.stream().flatMap(spec -> spec.getStoreGoodsSpecRelationList().stream()).toList();

            if (CollUtil.isNotEmpty(allSpecRelations)) {
                // 按规格组名称分组
                Map<String, List<StoreGoodsSpecRelationVO>> specGroupMap = allSpecRelations.stream().collect(Collectors.groupingBy(StoreGoodsSpecRelationVO::getGoodsSpecName));

                List<SpecGroupDTO> specGroups = new ArrayList<>();
                specGroupMap.forEach((specName, relations) -> {
                    if (CollUtil.isNotEmpty(relations)) {
                        SpecGroupDTO specGroup = new SpecGroupDTO();
                        // 设置规格组名称
                        specGroup.setName(specName);
                        // 设置规格组排序
                        specGroup.setOrder(relations.get(0).getGoodsSpecSort());

                        // 设置规格值列表（去重）
                        List<SpecDTO> children = relations.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StoreGoodsSpecRelationVO::getGoodsSpecDetailName))), set -> set.stream().map(relation -> {
                            SpecDTO spec = new SpecDTO();
                            spec.setName(relation.getGoodsSpecDetailName());
                            spec.setOrder(relation.getGoodsSpecDetailSort());
                            return spec;
                        }).toList()));
                        specGroup.setChildren(children);
                        specGroups.add(specGroup);
                    }
                });
                productDTO.setSpecGroups(specGroups);
            }
        }

        List<SkuDTO> skus = specDetailsList.stream().map(spec -> {
            SkuDTO sku = new SkuDTO();

            // 设置SKU图片
            Optional.ofNullable(spec.getImage()).ifPresent(image -> sku.setImage(image.getUrl()));

            // 设置SKU基本信息
            sku.setMdmSkuId(spec.getSkuId());
            //设置skuId
            if (!isNew) {
                sku.setId(storageRpcService.getSKuIdByMdmSkuId(spec.getSkuId(), mallShopId));
            }
            if (isNew) {
                //初始库存 仅新增sku时 可以使用
                sku.setInitStock(Optional.ofNullable(spec.getInventory()).orElse(0L));
            }
            // 设置初始销量
            sku.setInitSalesVolume(Optional.ofNullable(spec.getOnlineInitSales()).orElse(0L));
            // 设置限购类型
            Integer limitNum = Optional.ofNullable(spec.getLimitBuyNum()).orElse(0);
            if (limitNum > 0) {
                if (isMultipleSpec) {
                    sku.setLimitType(LimitType.SKU_LIMITED);
                } else {
                    sku.setLimitType(LimitType.PRODUCT_LIMITED);
                }
            } else {
                sku.setLimitType(LimitType.UNLIMITED);
            }

            if (!isMultipleSpec) {
                // 单规格得sku图片就是商品图片
                sku.setImage(productDTO.getPic());
            }

            // 设置限购数量
            sku.setLimitNum(limitNum);
            // 设置划线价
            sku.setPrice(spec.getLinePrice() == null ? 0L : AmountCalculateHelper.toMilli(spec.getLinePrice()));
            sku.setStockType(StockType.LIMITED);
            // 设置重量
            sku.setWeight(Optional.ofNullable(spec.getWeight()).orElse(new BigDecimal("0")));

            // 设置SKU价格
            Optional.ofNullable(spec.getSectionList()).filter(sections -> !sections.isEmpty()).map(sections -> sections.get(0)).ifPresent(section -> sku.setSalePrice(AmountCalculateHelper.toMilli(section.getSellingPrice())));

            // 设置SKU规格值
            if (isMultipleSpec) {
                List<String> specs = spec.getStoreGoodsSpecRelationList().stream().map(StoreGoodsSpecRelationVO::getGoodsSpecDetailName).toList();
                sku.setSpecs(specs);
            }

            return sku;
        }).toList();
        productDTO.setSkus(skus);
    }

    /**
     * 将图片地址集合转换为富文本格式
     *
     * @param imageUrls 图片地址集合
     * @return 富文本格式的图片字符串
     */
    private String convertImagesToRichText(List<String> imageUrls) {
        if (CollUtil.isEmpty(imageUrls)) {
            return "";
        }

        StringBuilder richText = new StringBuilder("<p>");
        for (String imageUrl : imageUrls) {
            richText.append(String.format("<img src=\"%s\" alt=\"\" data-href=\"\" style=\"\"/>", imageUrl));
        }
        richText.append("</p>");

        return richText.toString();
    }
}
