# # 使用 Ubuntu Jammy 基础镜像（基于 Eclipse Temurin 的镜像）
# FROM eclipse-temurin:17.0.15_6-jdk-jammy

# # 设置时区
# RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
#     && echo 'Asia/Shanghai' > /etc/timezone

# # 配置 APT 源为 Ubuntu 官方镜像（替换为阿里云或清华源）
# RUN sed -i 's|http://archive.ubuntu.com|http://cn.archive.ubuntu.com|g' /etc/apt/sources.list

# # 安装字体和依赖
# RUN apt update && apt install -y \
#     libfreetype6 \
#     fontconfig \
#     fonts-dejavu-core \
#     fonts-liberation \
#     fonts-noto-cjk \
#     fonts-wqy-zenhei \
#     && fc-cache -fv \
#     && apt clean \
#     && rm -rf /var/lib/apt/lists/*

FROM harbor.holderzone.com/library/eclipse-temurin:17.0.15_6-jdk-jammy-fonts

# 设置环境变量
ENV TZ=Asia/Shanghai

ADD config/ /config/
ADD gruul-mall-user-service-1.0.jar gruul-mall-user-service-1.0.jar
ADD lib/ /lib/
ENTRYPOINT ["java","-jar","-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:38724","-Djava.awt.headless=true","--add-opens=java.base/java.text=ALL-UNNAMED","--add-opens=java.base/java.lang=ALL-UNNAMED","--add-opens=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED","--add-opens=java.base/java.math=ALL-UNNAMED","--add-opens=java.base/java.util=ALL-UNNAMED","--add-opens=java.base/java.util.concurrent=ALL-UNNAMED","gruul-mall-user-service-1.0.jar"]