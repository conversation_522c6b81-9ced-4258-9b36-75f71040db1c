package com.medusa.gruul.user.service.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.dto.*;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.*;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.payment.api.model.pay.PayResult;
import com.medusa.gruul.user.service.model.dto.MemberCardOpenCardDealDTO;
import com.medusa.gruul.common.member.dto.MemberCardOpenCardPayCheckDTO;
import com.medusa.gruul.user.service.model.dto.MemberCardRechargeDealDTO;
import com.medusa.gruul.user.service.service.MemberCardService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员卡
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/card")
public class MemberCardController {
    private final MemberCardService memberCardService;
    private final MemberApiService memberApiService;

    /**
     * 获取支付会员卡
     *
     * @param memberInfoGuid 会员guid
     * @return 会员卡
     */
    @Log("获取支付会员卡")
    @GetMapping("payList")
    @PreAuthorize("@S.authenticated")
    public Result<List<PayMemberCardVO>> getPayMemberCardList(String memberInfoGuid) {
        return Result.ok(memberApiService.getPayMemberCardList(memberInfoGuid));
    }

    /**
     * 获取我的会员卡
     *
     * @param dto 请求参数
     * @return 会员卡
     */
    @Log("获取我的会员卡")
    @GetMapping("myList")
    @PreAuthorize("@S.authenticated")
    public Result<List<MyMemberCardVO>> getMyMemberCardList(@Valid MyMemberCardQueryDTO dto) {
        return Result.ok(memberApiService.getMyMemberCardList(dto));
    }

    /**
     * 获取会员卡发卡状态
     *
     * @param cardGuid 会员卡guid
     * @return 发卡状态
     */
    @Log("获取会员卡发卡状态")
    @GetMapping("sendStatus")
    @PreAuthorize("@S.authenticated")
    public Result<Integer> getSendStatus(String cardGuid) {
        return Result.ok(memberApiService.getSendStatus(cardGuid));
    }

    /**
     * 开通会员卡
     *
     * @param dto 请求参数
     * @return 开卡结果
     */
    @PostMapping(value = "/openCard")
    public Result<MemberCardOpenVO> openCard(@RequestBody MemberCardOpenDTO dto) {
        return Result.ok(memberCardService.openCard(dto));
    }

    /**
     * 开通会员卡（校验）
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    @PostMapping(value = "/openCardPayCheck")
    public Result<Void> openCardPayCheck(@RequestBody MemberCardOpenCardPayCheckDTO dto) {
        memberApiService.openCardPayCheck(dto);
        return Result.ok();
    }

    /**
     * 开通会员卡（付费）
     *
     * @param dto 请求参数
     * @return 支付结果
     */
    @Log("开通会员卡（付费）")
    @PostMapping("/openCardPay")
    public Result<PayResult> openCardPay(@RequestBody @Valid MemberCardOpenCardDealDTO dto) {
        return Result.ok(memberCardService.openCardPay(dto));
    }

    /**
     * 获取会员卡详情
     *
     * @param dto 请求参数
     * @return 会员卡
     */
    @Log("获取会员卡详情")
    @GetMapping("info")
    @PreAuthorize("@S.authenticated")
    public Result<MemberCardInfoVO> getMemberCardInfo(@Valid MemberCardInfoQueryDTO dto) {
        return Result.ok(memberApiService.getMemberCardInfo(dto));
    }

    /**
     * 查询会员卡余额合计
     *
     * @param dto 请求参数
     * @return 余额合计
     */
    @Log("查询会员卡余额合计")
    @GetMapping("balanceTotal")
    @PreAuthorize("@S.authenticated")
    public Result<MemberCardBalanceTotalVO> getMemberCardBalanceTotal(@Valid MemberCardBalanceQueryDTO dto) {
        return Result.ok(memberApiService.getMemberCardBalanceTotal(dto));
    }

    /**
     * 查询会员卡余额记录
     *
     * @param dto 请求参数
     * @return 余额记录
     */
    @Log("查询会员卡余额记录")
    @GetMapping("balanceRecord")
    @PreAuthorize("@S.authenticated")
    public Result<PageVO<MemberCardBalanceGroupVO>> getMemberCardBalanceRecord(@Valid MemberCardBalanceQueryDTO dto) {
        return Result.ok(memberApiService.getMemberCardBalanceRecord(dto));
    }

    /**
     * 查询会员卡二维码
     *
     * @param memberInfoCardGuid 会员持卡guid
     * @return 二维码
     */
    @Log("查询会员卡二维码")
    @GetMapping("qrcode")
    @PreAuthorize("@S.authenticated")
    public Result<MemberCardQrCodeVO> getQrcode(String memberInfoCardGuid) {
        return Result.ok(memberApiService.getQrcode(memberInfoCardGuid));
    }

    /**
     * 修改会员卡密码
     *
     * @param dto 请求参数
     * @return 结果
     */
    @Log("修改会员卡密码")
    @PostMapping("updatePwd")
    @PreAuthorize("@S.authenticated")
    public Result<Boolean> updatePwd(@RequestBody @Valid MemberCardPwdUpdateDTO dto) {
        return Result.ok(memberApiService.updatePwd(dto));
    }

    /**
     * 修改默认会员卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @Log("修改默认会员卡")
    @PostMapping(value = "/updateDefault")
    @PreAuthorize("@S.authenticated")
    public Result<Boolean> updateDefault(@RequestBody @Valid MemberCardDefaultUpdateDTO dto) {
        return Result.ok(memberApiService.updateDefault(dto));
    }

    /**
     * 绑定实体卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @Log("绑定实体卡")
    @PostMapping(value = "/bindPhysicalCard")
    @PreAuthorize("@S.authenticated")
    public Result<MemberCardBindResultVO> bindPhysicalCard(@RequestBody @Valid MemberCardBindPhysicalDTO dto) {
        return Result.ok(memberApiService.bindPhysicalCard(dto));
    }

    /**
     * 会员卡充值页面
     *
     * @param memberInfoCardGuid 会员持卡guid
     * @return 充值页面
     */
    @Log("会员卡充值页面")
    @GetMapping("rechargePage")
    @PreAuthorize("@S.authenticated")
    public Result<MemberCardRechargePageVO> rechargePage(String memberInfoCardGuid,
                                                         @RequestParam(value = "storeGuid", required = false, defaultValue = "0") String storeGuid) {
        return Result.ok(memberApiService.rechargePage(memberInfoCardGuid, storeGuid));
    }

    /**
     * 计算预计到账金额
     *
     * @param dto 请求参数
     * @return 查询结果
     */
    @Log("计算预计到账金额")
    @PostMapping("/calculatePreMoney")
    public Result<RechargeThresholdVO> calculatePreMoney(@RequestBody MemberCardRechargeCalculateDTO dto) {
        return Result.ok(memberApiService.calculatePreMoney(dto));
    }

    /**
     * 会员卡充值
     *
     * @param dto 请求参数
     * @return 支付结果
     */
    @Log("会员卡充值")
    @PostMapping("/recharge")
    public Result<PayResult> recharge(@RequestBody @Valid MemberCardRechargeDealDTO dto) {
        return Result.ok(memberCardService.recharge(dto));
    }

}
