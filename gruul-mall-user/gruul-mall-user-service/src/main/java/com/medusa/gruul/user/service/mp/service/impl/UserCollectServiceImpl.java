package com.medusa.gruul.user.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.user.service.mp.entity.UserCollect;
import com.medusa.gruul.user.service.mp.mapper.UserCollectMapper;
import com.medusa.gruul.user.service.mp.service.IUserCollectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 
 * 用户收藏 服务实现类
 * 
 *
 * <AUTHOR> @since 2022-08-01
 */
@Service
@RequiredArgsConstructor
public class UserCollectServiceImpl extends ServiceImpl<UserCollectMapper, UserCollect> implements IUserCollectService {


}