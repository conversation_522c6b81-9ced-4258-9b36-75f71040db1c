package com.medusa.gruul.user.service.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.GradeEquitiesInfoDetailVO;
import com.medusa.gruul.common.model.resp.Result;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

/**
 * 用户权益 控制层
 * <AUTHOR>
 * @version 1.0, 2025/5/16
 */
@RestController
@RequestMapping ("/user/member/rights")
@RequiredArgsConstructor
public class UserMemberRightsController {

	private final MemberApiService memberApiService;

	/**
	 * 获取权益信息
	 *
	 * @return 会员中心信息
	 */
	@Log ("会员中台-权益中心")
	@GetMapping ("/info/{rightsId}")
	@PreAuthorize ("@S.matcher().role(@S.R.USER).match()")
	public Result<GradeEquitiesInfoDetailVO> rightsInfo(@PathVariable Long rightsId) {
		return Result.ok(memberApiService.getGradeEquitiesInfoDetail(rightsId));
	}

}
