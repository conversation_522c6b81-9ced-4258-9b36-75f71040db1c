package com.medusa.gruul.user.service.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.vo.GradeEquitiesPreviewVO;
import com.medusa.gruul.common.member.vo.GradePreviewVO;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.user.api.model.vo.MemberAggregationInfoVO;
import com.medusa.gruul.user.service.service.MemberCardService;
import com.medusa.gruul.user.service.service.UserMemberRightsService;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户会员卡控制层
 *
 * <AUTHOR>
 * @Description UserMemberCardController.java
 * @date 2022-11-17 10:38
 */
@RestController
@RequestMapping("/user/member/card")
@RequiredArgsConstructor
public class UserMemberCardController {

    private final MemberCardService memberCardService;
    private final UserMemberRightsService userMemberRightsService;


    /**
     * 获取会员中心信息
     *
     * @return 会员中心信息
     */
    @Log("会员中心")
    @GetMapping("/info")
    @PreAuthorize("@S.matcher().role(@S.R.USER).match()")
    public Result<MemberAggregationInfoVO> memberCentre(Long userId) {
        return Result.ok(memberCardService.getMemberCentre(userId));
    }

    /**
     * 会员中心权益弹窗获取
     *
     * @param memberGradeId 会员等级id
     * @return 会员权益预览信息
     */
    @Log("会员中心权益弹窗获取")
    @GetMapping("/right/preview/{memberGradeId}")
    public Result<GradePreviewVO> memberRightsPreview(@PathVariable Long memberGradeId) {
        return Result.ok(userMemberRightsService.memberRightsPreview(memberGradeId));
    }




}
