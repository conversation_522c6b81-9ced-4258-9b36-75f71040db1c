package com.medusa.gruul.user.service.model.dto;

import com.medusa.gruul.common.model.enums.PayType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 会员卡开卡交易
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardOpenCardDealDTO {

    /**
     * 会员卡guid
     */
    @NotEmpty
    private String cardGuid;

    /**
     * 用户guid
     */
    @NotEmpty
    private String memberInfoGuid;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 支付渠道
     */
    @NotNull
    private PayType payType;

    /**
     * 支付金额
     */
    @NotNull
    private BigDecimal payAmount;

}
