package com.medusa.gruul.user.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.user.service.mp.entity.UserMemberRelevancyRights;
import com.medusa.gruul.user.service.mp.mapper.UserMemberRelevancyRightsMapper;
import com.medusa.gruul.user.service.mp.service.IUserMemberRelevancyRightsService;
import org.springframework.stereotype.Service;

/**
 * 实现层
 *
 * <AUTHOR>
 * @Description UserMemberRelevancyRightsServiceImpl.java
 * @date 2022-11-11 14:35
 */
@Service
public class UserMemberRelevancyRightsServiceImpl extends ServiceImpl<UserMemberRelevancyRightsMapper, UserMemberRelevancyRights> implements IUserMemberRelevancyRightsService {
}
