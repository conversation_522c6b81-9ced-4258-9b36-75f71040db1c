package com.medusa.gruul.user.service.mp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.member.dto.MemberQueryDTO;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.MemberBasicInfoVO;
import com.medusa.gruul.user.api.enums.MemberType;
import com.medusa.gruul.user.service.model.dto.ShopUserQueryDTO;
import com.medusa.gruul.user.service.model.vo.ShopUserVO;
import com.medusa.gruul.user.service.mp.entity.ShopUserAccount;
import com.medusa.gruul.user.service.mp.mapper.ShopUserAccountMapper;
import com.medusa.gruul.user.service.mp.service.IShopUserAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Service
@RequiredArgsConstructor
public class ShopUserAccountServiceImpl extends ServiceImpl<ShopUserAccountMapper, ShopUserAccount> implements IShopUserAccountService {

    private final MemberApiService memberApiService;

    @Override
    public IPage<ShopUserVO> getShopUserAccountList(ShopUserQueryDTO shopUserQuery) {
        IPage<ShopUserVO> pageData = baseMapper.getShopUserAccountList(shopUserQuery);
        if (CollUtil.isNotEmpty(pageData.getRecords())){
            List<ShopUserVO> records = pageData.getRecords();
            List<Long> ids = records.stream().map(ShopUserVO::getUserId).toList();
            List<MemberBasicInfoVO> memberBasicInfoVOS = memberApiService.batchGetMemberBasicInfo(new MemberQueryDTO().setMemberGuidList(ids.stream().map(String::valueOf).toList()));
            if (CollUtil.isNotEmpty(memberBasicInfoVOS)){
                Map<String, MemberBasicInfoVO> memberBasicInfoVOMap = memberBasicInfoVOS.stream().collect(Collectors.toMap(MemberBasicInfoVO::getGuid, memberBasicInfoVO -> memberBasicInfoVO));
                records.forEach(shopUserVO -> {
                    MemberBasicInfoVO memberBasicInfoVO = memberBasicInfoVOMap.get(shopUserVO.getUserId().toString());
                    if (memberBasicInfoVO != null){
                        shopUserVO.setMemberType(StrUtil.isNotEmpty(memberBasicInfoVO.getMemberPaidGradeInfoGuid()) ? MemberType.PAID_MEMBER : MemberType.FREE_MEMBER);
                        shopUserVO.setRankCode(StrUtil.isNotEmpty(memberBasicInfoVO.getMemberPaidGradeInfoGuid()) ? memberBasicInfoVO.getPaidVipGrade() : memberBasicInfoVO.getVipGrade());
                    }
                });
            }
        }
        return pageData;
    }

    @Override
    public ShopUserVO getShopUserAccountDetail(Long shopId, Long userId) {
        return baseMapper.getShopUserAccountDetail(shopId,userId);
    }
}
