package com.medusa.gruul.user.service.model.dto;

import com.medusa.gruul.common.model.enums.PayType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 会员卡充值交易
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardRechargeDealDTO {

    /**
     * 会员guid
     */
    @NotBlank(message = "会员guid不能为空")
    private String memberInfoGuid;

    /**
     * 会员持卡guid
     */
    @NotBlank(message = "会员持卡guid不能为空")
    private String memberInfoCardGuid;

    /**
     * 支付渠道
     */
    @NotNull
    private PayType payType;

    /**
     * 支付金额
     */
    @NotNull
    private BigDecimal payAmount;

    private String storeGuid;

    private String storeName;

}
