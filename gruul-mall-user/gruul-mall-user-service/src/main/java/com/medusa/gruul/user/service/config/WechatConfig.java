package com.medusa.gruul.user.service.config;

import com.medusa.gruul.common.wechat.IWechatConfigGetter;
import com.medusa.gruul.user.service.service.addon.UserAddonSupporter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 用户服务微信配置类
 * <AUTHOR>
 * @date 2025/07/09
 */
@Configuration
@RequiredArgsConstructor
public class WechatConfig {
    
    private final UserAddonSupporter userAddonSupporter;
    
    @Bean
    public IWechatConfigGetter wechatConfigGetter() {
        return userAddonSupporter::getWechatConfig;
    }
} 