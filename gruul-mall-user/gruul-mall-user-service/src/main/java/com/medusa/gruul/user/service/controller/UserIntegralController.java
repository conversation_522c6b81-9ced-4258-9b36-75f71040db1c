package com.medusa.gruul.user.service.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.dto.AppletIntegralDetailDTO;
import com.medusa.gruul.common.member.dto.AppletIntegralDetailPageDTO;
import com.medusa.gruul.common.member.dto.AppletIntegralPageDTO;
import com.medusa.gruul.common.member.service.MemberIntegralApiService;
import com.medusa.gruul.common.member.vo.AppletIntegralDetailVO;
import com.medusa.gruul.common.member.vo.PageVO;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.user.service.model.dto.IntegralDetailQueryDTO;
import com.medusa.gruul.user.service.model.dto.UserIntegralChangeDTO;
import com.medusa.gruul.user.service.model.vo.UserIntegralDetailVO;
import com.medusa.gruul.user.service.mp.service.IUserIntegralDetailService;
import com.medusa.gruul.user.service.service.UserIntegralService;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/2/9
 * @time 11:32
 **/

@RestController
@RequestMapping("/user/integral")
@RequiredArgsConstructor
@Slf4j
public class UserIntegralController {


    private final UserIntegralService userIntegralService;
    private final IUserIntegralDetailService iUserIntegralDetailService;

    private final MemberIntegralApiService memberIntegralApiService;


    /**
     * 查看用户剩余积分
     */
    @Log("查看用户剩余积分")
    @PreAuthorize("""
            @S.matcher()
            .any(@S.ROLES,@S.PLATFORM_ADMIN,@S.USER)
            .or(@S.consumer().eq(@S.ROLES,@S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'vip:base'))
            .match()
            """)
    @GetMapping("/system/total")
    public Result<Long> getIntegralTotal(Long userId) {

        userId = Option.of(userId).getOrElse(() -> ISecurity.userMust().getId());

        return Result.ok(
                this.userIntegralService.getIntegralTotalByUserId(userId)
        );
    }


    /**
     * 用户积分变化
     */
    @Log("用户积分调整")
    @PreAuthorize("@S.platformPerm('vip:base')")
    @PostMapping("/system/change")
    public Result<Boolean> integralChange(@RequestBody @Valid UserIntegralChangeDTO userIntegralChangeDTO) {

        return Result.ok(
                this.userIntegralService.changeIntegralBySystem(userIntegralChangeDTO)
        );
    }


    /**
     * 用户积分明细List
     *
     * @param integralDetailQuery 查询参数
     * @return 积分明细
     */
    @Log("用户积分明细List")
    @PostMapping("/detail/info")
    @PreAuthorize("""
            @S.matcher()
            .any(@S.ROLES,@S.PLATFORM_ADMIN,@S.USER)
            .or(@S.consumer().eq(@S.ROLES,@S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'vip:base'))
            .match()
            """)
    public Result<AppletIntegralPageDTO> getUserIntegralDetailList(
            @RequestBody final AppletIntegralDetailPageDTO integralDetailQuery) {
        integralDetailQuery.setMemberInfoGuid(ISecurity.userMust().getId().toString());
        log.info("获取用户积分明细List, integralDetailQuery={}", integralDetailQuery);
        AppletIntegralPageDTO pageDTO = new AppletIntegralPageDTO();
        PageVO<?> page = memberIntegralApiService.getAppletIntegralDetail(integralDetailQuery);

        List<?> records = page.getRecords();
        PageVO<AppletIntegralDetailDTO> result = new PageVO<>();
        if (CollUtil.isEmpty(records)) {
            pageDTO.setPageVO(result);
            return Result.ok(pageDTO);
        }
        if (Objects.nonNull(integralDetailQuery.getTaskAction())) {
            integralDetailQuery.setPageSize(20);
            // 创建新的分页对象并复制分页信息
            PageVO<AppletIntegralDetailDTO> resultDto = new PageVO<>();
            PageVO<?> pageVO = memberIntegralApiService.getAppletIntegralDetail(integralDetailQuery);
            List<?> resultList = pageVO.getRecords();
            AppletIntegralDetailVO detailVO = getAppletIntegralDetailVO(pageVO, resultDto, resultList);
            pageDTO.setEarnIntegral(Objects.requireNonNull(detailVO).getMonthIntegral());
        }

        // 创建新的分页对象并复制分页信息
        AppletIntegralDetailVO detailVO = getAppletIntegralDetailVO(page, result, records);
        List<AppletIntegralDetailDTO> dtoList = detailVO != null ? detailVO.getIntegralDetailDTOS() : null;
        result.setRecords(dtoList != null ? dtoList : new ArrayList<>());

        //获取总记录数和总页数
        pageDTO.setPageVO(result);
        return Result.ok(pageDTO);
    }

    @Nullable
    private static AppletIntegralDetailVO getAppletIntegralDetailVO(PageVO<?> page, PageVO<AppletIntegralDetailDTO> result, List<?> records) {
        BeanUtils.copyProperties(page, result, "records");

        // 将JSONObject转换为AppletIntegralDetailVO
        return JSON.parseObject(JSON.toJSONString(records.get(0)), AppletIntegralDetailVO.class);
    }
}
