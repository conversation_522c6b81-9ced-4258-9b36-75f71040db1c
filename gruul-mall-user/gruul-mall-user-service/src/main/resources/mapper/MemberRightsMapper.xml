<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.user.service.mp.mapper.MemberRightsMapper">

    <resultMap id="BaseResultVOMap" type="com.medusa.gruul.user.api.model.vo.RelevancyRightsVO">
        <result column="id" property="memberRightsId"/>
        <result column="rights_type" property="rightsType"/>
        <result column="rights_name" property="rightsName"/>
        <result column="rights_explain" property="rightsExplain"/>
        <result column="rights_icon" property="rightsIcon"/>
    </resultMap>

    <select id="queryRightByIds" resultMap="BaseResultVOMap">
        SELECT
        id ,rights_type,rights_name,rights_explain,rights_icon
        FROM
        t_member_rights
        WHERE
        deleted = 0
        AND rights_switch = 1 AND
        id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY rights_type
    </select>

    <select id="checkRightInUse" resultType="java.lang.Integer">
        select count(*) from t_user_member_relevancy_rights rights inner join
                             t_user_free_member member on rights.member_id = member.id
        where member.deleted=0 and rights.deleted=0
          and rights.member_rights_id=#{rightId}
    </select>
</mapper>