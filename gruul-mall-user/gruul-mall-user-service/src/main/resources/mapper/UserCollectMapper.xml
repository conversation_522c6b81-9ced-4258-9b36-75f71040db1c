<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.user.service.mp.mapper.UserCollectMapper">

    <select id="getShopProductCollection" resultType="com.medusa.gruul.user.api.model.UserCollectVO">
        SELECT
            product_id,
            COUNT(*) productCollect
        FROM
            t_user_collect
        WHERE
            shop_id = #{shopId}
          AND deleted = 0
        GROUP BY
            product_id
        ORDER BY
            productCollect DESC
        LIMIT 7
    </select>
</mapper>
