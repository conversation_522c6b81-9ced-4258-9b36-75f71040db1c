<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta content="width=device-width, initial-scale=1.0" name="viewport"><meta content="smart-doc" name="generator"><title>addon-live</title><link href="font.css" rel="stylesheet"><link href="AllInOne.css?v=1694496230674" rel="stylesheet"/><style>.literalblock pre,.listingblock pre:not(.highlight),.listingblock pre[class="highlight"],.listingblock pre[class^="highlight "],.listingblock pre.CodeRay,.listingblock pre.prettyprint{background:#f7f7f8}.hljs{padding:0}</style><script src="highlight.min.js"></script><script src="jquery.min.js"></script></head><body class="book toc2 toc-left"><div id="header"><h1>addon-live</h1><div class="toc2" id="toc"><div id="book-search-input"><input id="search" placeholder="Type to search" type="text"></div><div id="toctitle"><span>API Reference</span></div><ul class="sectlevel1" id="accordion"><li class="open"><a class="dd" href="#_1_直播回调">1.直播回调</a><ul class="sectlevel2"><li><a href="#_1_1_1_直播回调">1.1.直播回调</a></li></ul></li><li class="open"><a class="dd" href="#_2_用户控制器">2.用户控制器</a><ul class="sectlevel2"><li><a href="#_1_2_1_预约直播间">2.1.预约直播间</a></li><li><a href="#_1_2_2_用户关注主播">2.2.用户关注主播</a></li><li><a href="#_1_2_3_关注直播间列表">2.3.关注直播间列表</a></li><li><a href="#_1_2_4_用户发现直播间">2.4.用户发现直播间</a></li><li><a href="#_1_2_5_C端获取主播信息">2.5.C端获取主播信息</a></li><li><a href="#_1_2_6_用户随机获取一条直播间信息">2.6.用户随机获取一条直播间信息</a></li><li><a href="#_1_2_7_直播分享图文字转图片">2.7.直播分享图文字转图片</a></li><li><a href="#_1_2_8_用户进入直播间时，添加直播间观看人数">2.8.用户进入直播间时，添加直播间观看人数</a></li><li><a href="#_1_2_9_用户是否关注 前端需要过滤一遍用户登陆状态">2.9.用户是否关注 前端需要过滤一遍用户登陆状态</a></li></ul></li><li class="open"><a class="dd" href="#_3_主播直播间控制器">3.主播直播间控制器</a><ul class="sectlevel2"><li><a href="#_1_3_1_查询是否有已经开播的直播间">3.1.查询是否有已经开播的直播间</a></li><li><a href="#_1_3_2_创建直播间">3.2.创建直播间</a></li><li><a href="#_1_3_3_查询主播对应的直播间列表">3.3.查询主播对应的直播间列表</a></li><li><a href="#_1_3_4_直播间下播">3.4.直播间下播</a></li><li><a href="#_1_3_5_删除直播间">3.5.删除直播间</a></li><li><a href="#_1_3_6_直播间详情">3.6.直播间详情</a></li><li><a href="#_1_3_7_直播间聊天室userSig">3.7.直播间聊天室userSig</a></li></ul></li></ul></div></div><div id="content"><div id="preamble"><div class="sectionbody"><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Version</th><th class="tableblock halign-left valign-top">Update Time</th><th class="tableblock halign-left valign-top">Status</th><th class="tableblock halign-left valign-top">Author</th><th class="tableblock halign-left valign-top">Description</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">v2023-09-12 13:23:50</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">2023-09-12 13:23:50</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">auto</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">@yusi</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">Created by smart-doc</p></td></tr></tbody></table></div></div><div class="sect1"><h2 id="_1_1_直播回调"><a class="anchor" href="#_1_1_直播回调"></a><a class="link" href="#_1_1_直播回调">1.直播回调</a></h2><div class="sectionbody"><div class="sect2" id="91888adeea99aa980041e18781286cc3"><h3 id="_1_1_1_直播回调"><a class="anchor" href="#_1_1_1_直播回调"></a><a class="link" href="#_1_1_1_直播回调">1.1.直播回调</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/api/notify" id="91888adeea99aa980041e18781286cc3-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/api/notify">&nbsp;/addon-live/api/notify</a></p></div><div class="paragraph" data-method="POST" id="91888adeea99aa980041e18781286cc3-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="91888adeea99aa980041e18781286cc3-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>直播回调</p></div><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -i /addon-live/api/notify</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": ""
}</code></pre></div></div></div></div></div><div class="sect1"><h2 id="_1_2_用户控制器"><a class="anchor" href="#_1_2_用户控制器"></a><a class="link" href="#_1_2_用户控制器">2.用户控制器</a></h2><div class="sectionbody"><div class="sect2" id="ff91d51b09f5db13be058e2f92c21e09"><h3 id="_1_2_1_预约直播间"><a class="anchor" href="#_1_2_1_预约直播间"></a><a class="link" href="#_1_2_1_预约直播间">2.1.预约直播间</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/add/reservation" id="ff91d51b09f5db13be058e2f92c21e09-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/add/reservation">&nbsp;/addon-live/user/add/reservation</a></p></div><div class="paragraph" data-method="POST" id="ff91d51b09f5db13be058e2f92c21e09-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/json" id="ff91d51b09f5db13be058e2f92c21e09-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>预约直播间</p></div><div class="paragraph"><p><strong>Body-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">isReservation</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否关注</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -i /addon-live/user/add/reservation --data '{
  "liveId": 0,
  "shopId": 0,
  "isReservation": true
}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="df41b3ef179ed33b474ede2fff7d57ff"><h3 id="_1_2_2_用户关注主播"><a class="anchor" href="#_1_2_2_用户关注主播"></a><a class="link" href="#_1_2_2_用户关注主播">2.2.用户关注主播</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/add/follow" id="df41b3ef179ed33b474ede2fff7d57ff-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/add/follow">&nbsp;/addon-live/user/add/follow</a></p></div><div class="paragraph" data-method="POST" id="df41b3ef179ed33b474ede2fff7d57ff-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/json" id="df41b3ef179ed33b474ede2fff7d57ff-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>用户关注主播</p></div><div class="paragraph"><p><strong>Body-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">isFollow</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">关注行为</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -i /addon-live/user/add/follow --data '{
  "anchorId": 0,
  "shopId": 0,
  "isFollow": true
}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="35a2d837b74414f5b01f84677869f4d1"><h3 id="_1_2_3_关注直播间列表"><a class="anchor" href="#_1_2_3_关注直播间列表"></a><a class="link" href="#_1_2_3_关注直播间列表">2.3.关注直播间列表</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/follow/live/list" id="35a2d837b74414f5b01f84677869f4d1-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/follow/live/list">&nbsp;/addon-live/user/follow/live/list</a></p></div><div class="paragraph" data-method="GET" id="35a2d837b74414f5b01f84677869f4d1-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="35a2d837b74414f5b01f84677869f4d1-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>关注直播间列表</p></div><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">观看人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->休息中;3-->已结束;4-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─isReservation</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否预约</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">keyword</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询关键字</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/follow/live/list?pages=0&liveId=0&shopId=0&shopType=SELF_OWNED&viewership=0&status=NOT_STARTED&isReservation=true&beginTime=yyyy-MM-dd HH:mm:ss&endTime=yyyy-MM-dd HH:mm:ss&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&orders[0].column=&orders[0].asc=true&keyword=&countId=</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">观看人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->休息中;3-->已结束;4-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─isReservation</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否预约</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "liveId": 0,
        "shopId": 0,
        "shopName": "",
        "shopLogo": "",
        "shopType": "SELF_OWNED",
        "viewership": 0,
        "liveTitle": "",
        "pic": "",
        "pushAddress": "",
        "pullAddress": "",
        "status": "NOT_STARTED",
        "isReservation": true,
        "beginTime": "yyyy-MM-dd HH:mm:ss",
        "endTime": "yyyy-MM-dd HH:mm:ss"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre></div></div></div><div class="sect2" id="8500e79041abd5a29cf2019957d51650"><h3 id="_1_2_4_用户发现直播间"><a class="anchor" href="#_1_2_4_用户发现直播间"></a><a class="link" href="#_1_2_4_用户发现直播间">2.4.用户发现直播间</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/discover/live/list" id="8500e79041abd5a29cf2019957d51650-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/discover/live/list">&nbsp;/addon-live/user/discover/live/list</a></p></div><div class="paragraph" data-method="GET" id="8500e79041abd5a29cf2019957d51650-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="8500e79041abd5a29cf2019957d51650-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>用户发现直播间</p></div><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">观看人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->休息中;3-->已结束;4-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─isReservation</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否预约</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">keyword</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询关键字</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/discover/live/list?pages=0&liveId=0&shopId=0&shopType=SELF_OWNED&viewership=0&status=NOT_STARTED&isReservation=true&beginTime=yyyy-MM-dd HH:mm:ss&endTime=yyyy-MM-dd HH:mm:ss&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&countId=&orders[0].asc=true&keyword=&orders[0].column=</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">观看人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->休息中;3-->已结束;4-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─isReservation</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否预约</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "liveId": 0,
        "shopId": 0,
        "shopName": "",
        "shopLogo": "",
        "shopType": "SELF_OWNED",
        "viewership": 0,
        "liveTitle": "",
        "pic": "",
        "pushAddress": "",
        "pullAddress": "",
        "status": "NOT_STARTED",
        "isReservation": true,
        "beginTime": "yyyy-MM-dd HH:mm:ss",
        "endTime": "yyyy-MM-dd HH:mm:ss"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre></div></div></div><div class="sect2" id="73ff52222a666e15b709a2c18e5b58b4"><h3 id="_1_2_5_C端获取主播信息"><a class="anchor" href="#_1_2_5_C端获取主播信息"></a><a class="link" href="#_1_2_5_C端获取主播信息">2.5.C端获取主播信息</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/message" id="73ff52222a666e15b709a2c18e5b58b4-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/message">&nbsp;/addon-live/user/message</a></p></div><div class="paragraph" data-method="GET" id="73ff52222a666e15b709a2c18e5b58b4-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="73ff52222a666e15b709a2c18e5b58b4-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>C端获取主播信息</p></div><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/message</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorNickname</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播昵称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorIcon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播头像</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播状态</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─gender</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">性别</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─phone</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">手机号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─followCount</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">粉丝数量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">观看数量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─duration</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">number</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播时长</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "shopId": 0,
    "anchorNickname": "",
    "anchorSynopsis": "",
    "anchorIcon": "",
    "status": "NORMAL",
    "gender": "UNKNOWN",
    "phone": "",
    "followCount": 0,
    "viewership": 0,
    "duration": 0
  }
}</code></pre></div></div></div><div class="sect2" id="7ba6e1d1a2e8e9f3b33a562e38a3a863"><h3 id="_1_2_6_用户随机获取一条直播间信息"><a class="anchor" href="#_1_2_6_用户随机获取一条直播间信息"></a><a class="link" href="#_1_2_6_用户随机获取一条直播间信息">2.6.用户随机获取一条直播间信息</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/random/view/{id}" id="7ba6e1d1a2e8e9f3b33a562e38a3a863-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/random/view/{id}">&nbsp;/addon-live/user/random/view/{id}</a></p></div><div class="paragraph" data-method="GET" id="7ba6e1d1a2e8e9f3b33a562e38a3a863-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="7ba6e1d1a2e8e9f3b33a562e38a3a863-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>用户随机获取一条直播间信息</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/random/view/{id}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">id
{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorNickname</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─streamName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流stream name</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中2-->已结束;3-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchor</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">id
{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─phone</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">手机号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorNickname</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播昵称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorIcon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播头像</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播状态</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─gender</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">性别</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">'观看人数'</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─duration</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">number</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">'直播时长'</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "createTime": "yyyy-MM-dd HH:mm:ss",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "version": 0,
    "deleted": true,
    "anchorId": 0,
    "anchorNickname": "",
    "shopId": 0,
    "shopName": "",
    "shopType": "SELF_OWNED",
    "shopLogo": "",
    "liveTitle": "",
    "liveSynopsis": "",
    "pic": "",
    "streamName": "",
    "pushAddress": "",
    "pullAddress": "",
    "beginTime": "yyyy-MM-dd HH:mm:ss",
    "endTime": "yyyy-MM-dd HH:mm:ss",
    "status": "NOT_STARTED",
    "anchor": {
      "id": 0,
      "createTime": "yyyy-MM-dd HH:mm:ss",
      "updateTime": "yyyy-MM-dd HH:mm:ss",
      "version": 0,
      "deleted": true,
      "shopId": 0,
      "userId": 0,
      "phone": "",
      "anchorNickname": "",
      "anchorSynopsis": "",
      "anchorIcon": "",
      "status": "NORMAL",
      "gender": "UNKNOWN"
    },
    "viewership": 0,
    "duration": 0
  }
}</code></pre></div></div></div><div class="sect2" id="d82c2db5d70d8754ea8e9c3e4fa55b77"><h3 id="_1_2_7_直播分享图文字转图片"><a class="anchor" href="#_1_2_7_直播分享图文字转图片"></a><a class="link" href="#_1_2_7_直播分享图文字转图片">2.7.直播分享图文字转图片</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/characters" id="d82c2db5d70d8754ea8e9c3e4fa55b77-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/characters">&nbsp;/addon-live/user/characters</a></p></div><div class="paragraph" data-method="GET" id="d82c2db5d70d8754ea8e9c3e4fa55b77-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="d82c2db5d70d8754ea8e9c3e4fa55b77-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>直播分享图文字转图片</p></div><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">   直播标题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/characters?liveTitle=&liveSynopsis=</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": ""
}</code></pre></div></div></div><div class="sect2" id="da50c46eae108c65467090cdebe9a5de"><h3 id="_1_2_8_用户进入直播间时，添加直播间观看人数"><a class="anchor" href="#_1_2_8_用户进入直播间时，添加直播间观看人数"></a><a class="link" href="#_1_2_8_用户进入直播间时，添加直播间观看人数">2.8.用户进入直播间时，添加直播间观看人数</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/viewership/{liveId}" id="da50c46eae108c65467090cdebe9a5de-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/viewership/{liveId}">&nbsp;/addon-live/user/viewership/{liveId}</a></p></div><div class="paragraph" data-method="POST" id="da50c46eae108c65467090cdebe9a5de-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="da50c46eae108c65467090cdebe9a5de-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>用户进入直播间时，添加直播间观看人数</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">liveId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -i /addon-live/user/viewership/{liveId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="784d76d208a81750bb52474dfa90ba03"><h3 id="_1_2_9_用户是否关注 前端需要过滤一遍用户登陆状态"><a class="anchor" href="#_1_2_9_用户是否关注 前端需要过滤一遍用户登陆状态"></a><a class="link" href="#_1_2_9_用户是否关注 前端需要过滤一遍用户登陆状态">2.9.用户是否关注 前端需要过滤一遍用户登陆状态</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/user/viewership/status/{anchorId}" id="784d76d208a81750bb52474dfa90ba03-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/user/viewership/status/{anchorId}">&nbsp;/addon-live/user/viewership/status/{anchorId}</a></p></div><div class="paragraph" data-method="GET" id="784d76d208a81750bb52474dfa90ba03-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="784d76d208a81750bb52474dfa90ba03-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>用户是否关注 前端需要过滤一遍用户登陆状态</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/user/viewership/status/{anchorId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": true
}</code></pre></div></div></div></div></div><div class="sect1"><h2 id="_1_3_主播直播间控制器"><a class="anchor" href="#_1_3_主播直播间控制器"></a><a class="link" href="#_1_3_主播直播间控制器">3.主播直播间控制器</a></h2><div class="sectionbody"><div class="sect2" id="7addbd318c4a431aefbe330755da1b29"><h3 id="_1_3_1_查询是否有已经开播的直播间"><a class="anchor" href="#_1_3_1_查询是否有已经开播的直播间"></a><a class="link" href="#_1_3_1_查询是否有已经开播的直播间">3.1.查询是否有已经开播的直播间</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/begin/{id}" id="7addbd318c4a431aefbe330755da1b29-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/begin/{id}">&nbsp;/addon-live/live/room/begin/{id}</a></p></div><div class="paragraph" data-method="GET" id="7addbd318c4a431aefbe330755da1b29-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="7addbd318c4a431aefbe330755da1b29-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>查询是否有已经开播的直播间</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/live/room/begin/{id}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": true
}</code></pre></div></div></div><div class="sect2" id="52c0979fe448a891e05faeeb06d75da8"><h3 id="_1_3_2_创建直播间"><a class="anchor" href="#_1_3_2_创建直播间"></a><a class="link" href="#_1_3_2_创建直播间">3.2.创建直播间</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/create" id="52c0979fe448a891e05faeeb06d75da8-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/create">&nbsp;/addon-live/live/room/create</a></p></div><div class="paragraph" data-method="POST" id="52c0979fe448a891e05faeeb06d75da8-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/json" id="52c0979fe448a891e05faeeb06d75da8-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>创建直播间</p></div><div class="paragraph"><p><strong>Body-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">title</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">标题
Validate[max: 10; ]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">简介
Validate[max: 15; ]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">预计开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -i /addon-live/live/room/create --data '{
  "title": "",
  "liveSynopsis": "",
  "pic": "",
  "userId": 0,
  "beginTime": "yyyy-MM-dd HH:mm:ss"
}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "pushAddress": ""
  }
}</code></pre></div></div></div><div class="sect2" id="0848f83f005c1d0b2b7e98e98505ba31"><h3 id="_1_3_3_查询主播对应的直播间列表"><a class="anchor" href="#_1_3_3_查询主播对应的直播间列表"></a><a class="link" href="#_1_3_3_查询主播对应的直播间列表">3.3.查询主播对应的直播间列表</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/anchor/list" id="0848f83f005c1d0b2b7e98e98505ba31-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/anchor/list">&nbsp;/addon-live/live/room/anchor/list</a></p></div><div class="paragraph" data-method="GET" id="0848f83f005c1d0b2b7e98e98505ba31-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="0848f83f005c1d0b2b7e98e98505ba31-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>查询主播对应的直播间列表</p></div><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播标题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->已结束;3-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/live/room/anchor/list?pages=0&id=0&anchorId=0&userId=0&beginTime=yyyy-MM-dd HH:mm:ss&endTime=yyyy-MM-dd HH:mm:ss&status=NOT_STARTED&createTime=yyyy-MM-dd HH:mm:ss&updateTime=yyyy-MM-dd HH:mm:ss&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&orders[0].column=&records[0].shopId=&countId=&records[0].liveSynopsis=&records[0].updateTime=yyyy-MM-dd HH:mm:ss&records[0].userId=0&records[0].createTime=yyyy-MM-dd HH:mm:ss&records[0].endTime=yyyy-MM-dd HH:mm:ss&records[0].pushAddress=&records[0].pullAddress=&records[0].pic=&records[0].anchorId=0&orders[0].asc=true&records[0].id=0&records[0].status=NOT_STARTED&records[0].beginTime=yyyy-MM-dd HH:mm:ss&records[0].liveTitle=</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播标题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中；2-->已结束;3-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "id": 0,
        "anchorId": 0,
        "userId": 0,
        "liveTitle": "",
        "shopId": "",
        "liveSynopsis": "",
        "pic": "",
        "pushAddress": "",
        "pullAddress": "",
        "beginTime": "yyyy-MM-dd HH:mm:ss",
        "endTime": "yyyy-MM-dd HH:mm:ss",
        "status": "NOT_STARTED",
        "createTime": "yyyy-MM-dd HH:mm:ss",
        "updateTime": "yyyy-MM-dd HH:mm:ss"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre></div></div></div><div class="sect2" id="c857f846c6017133ca111735b00565d2"><h3 id="_1_3_4_直播间下播"><a class="anchor" href="#_1_3_4_直播间下播"></a><a class="link" href="#_1_3_4_直播间下播">3.4.直播间下播</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/lower/broadcast/{id}" id="c857f846c6017133ca111735b00565d2-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/lower/broadcast/{id}">&nbsp;/addon-live/live/room/lower/broadcast/{id}</a></p></div><div class="paragraph" data-method="PUT" id="c857f846c6017133ca111735b00565d2-method"><p><strong>Type:&nbsp;</strong>PUT</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="c857f846c6017133ca111735b00565d2-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>直播间下播</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X PUT -i /addon-live/live/room/lower/broadcast/{id}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="efca8de41164ae499f7ffc0fa343ec3b"><h3 id="_1_3_5_删除直播间"><a class="anchor" href="#_1_3_5_删除直播间"></a><a class="link" href="#_1_3_5_删除直播间">3.5.删除直播间</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/deleted/{id}" id="efca8de41164ae499f7ffc0fa343ec3b-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/deleted/{id}">&nbsp;/addon-live/live/room/deleted/{id}</a></p></div><div class="paragraph" data-method="DELETE" id="efca8de41164ae499f7ffc0fa343ec3b-method"><p><strong>Type:&nbsp;</strong>DELETE</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="efca8de41164ae499f7ffc0fa343ec3b-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>删除直播间</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X DELETE -i /addon-live/live/room/deleted/{id}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="f20a8a937bc45fdc450a4226d388602d"><h3 id="_1_3_6_直播间详情"><a class="anchor" href="#_1_3_6_直播间详情"></a><a class="link" href="#_1_3_6_直播间详情">3.6.直播间详情</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/detail/{id}" id="f20a8a937bc45fdc450a4226d388602d-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/detail/{id}">&nbsp;/addon-live/live/room/detail/{id}</a></p></div><div class="paragraph" data-method="GET" id="f20a8a937bc45fdc450a4226d388602d-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="f20a8a937bc45fdc450a4226d388602d-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>直播间详情</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/live/room/detail/{id}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">id
{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchorNickname</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺类型<br/>(See: 店铺类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopLogo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺logo</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveTitle</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播主题</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─liveSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">封面图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─streamName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流stream name</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pushAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">推流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pullAddress</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">拉流地址</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─beginTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">下播时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">直播间状态 0-->未开播；1-->直播中2-->已结束;3-->违规下播</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─anchor</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">id
{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─phone</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">手机号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorNickname</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播昵称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorSynopsis</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播简介</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─anchorIcon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播头像</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─status</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主播状态</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─gender</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">性别</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─viewership</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">'观看人数'</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─duration</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">number</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">'直播时长'</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "createTime": "yyyy-MM-dd HH:mm:ss",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "version": 0,
    "deleted": true,
    "anchorId": 0,
    "anchorNickname": "",
    "shopId": 0,
    "shopName": "",
    "shopType": "SELF_OWNED",
    "shopLogo": "",
    "liveTitle": "",
    "liveSynopsis": "",
    "pic": "",
    "streamName": "",
    "pushAddress": "",
    "pullAddress": "",
    "beginTime": "yyyy-MM-dd HH:mm:ss",
    "endTime": "yyyy-MM-dd HH:mm:ss",
    "status": "NOT_STARTED",
    "anchor": {
      "id": 0,
      "createTime": "yyyy-MM-dd HH:mm:ss",
      "updateTime": "yyyy-MM-dd HH:mm:ss",
      "version": 0,
      "deleted": true,
      "shopId": 0,
      "userId": 0,
      "phone": "",
      "anchorNickname": "",
      "anchorSynopsis": "",
      "anchorIcon": "",
      "status": "NORMAL",
      "gender": "UNKNOWN"
    },
    "viewership": 0,
    "duration": 0
  }
}</code></pre></div></div></div><div class="sect2" id="86b115fd074ee880c2c122aec393d478"><h3 id="_1_3_7_直播间聊天室userSig"><a class="anchor" href="#_1_3_7_直播间聊天室userSig"></a><a class="link" href="#_1_3_7_直播间聊天室userSig">3.7.直播间聊天室userSig</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-live/live/room/userSig/{userId}" id="86b115fd074ee880c2c122aec393d478-url"><p><strong>URL:</strong><a class="bare" href="/addon-live/live/room/userSig/{userId}">&nbsp;/addon-live/live/room/userSig/{userId}</a></p></div><div class="paragraph" data-method="GET" id="86b115fd074ee880c2c122aec393d478-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>miskw</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="86b115fd074ee880c2c122aec393d478-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>直播间聊天室userSig</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">userId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">用户 ID</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-live/live/room/userSig/{userId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": ""
}</code></pre></div></div></div></div></div><footer class="page-footer"><span class="copyright">Generated by smart-doc at 2023-09-12 13:23:50</span><span class="footer-modification">Suggestions,contact,support and error reporting on<a href="https://gitee.com/smart-doc-team/smart-doc" target="_blank">&nbsp;Gitee&nbsp;</a>or<a href="https://github.com/smart-doc-group/smart-doc.git" target="_blank">&nbsp;Github</a></span></footer><div href="javascript:void(0)" id="toTop"><img id="upArrow" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABlUlEQVRIS+2UvUvDQBiH398Rly4udnARwUXs4qAIOigI4iL30dTZ2T9AcNPVvUsXF7uYttdScNDFRRAnB11cFFwKxcXBJTQnJ6lEbRI/CIiY9e6e5/e+9+ZAGX/ImE9/QKCU2jfGbGTQqq4xZgtSyisiKmQgIAAVCCFWAGxnIOhqrdd/xyUrpRZsP40xSwA6AI57vd5eq9W6T6s8tQIppSKi+gDQNREprfVNkiRRwDlfY4xZ+FAIuSOi8Qjw0nEc5XnebZwkViClXA2T5+xhY8xus9ncEUJMAziITN5FEARuXLsGCoQQywBs8uEovJ+Scz7FGDuMSM4cx3E9z+u8r+SDQEq5SEQ1IhoZBE+QnBKRq7V+iEreCDjn84wxCx9NgidITnK5nFutVh/7e14FSqnZIAhqAMY+A4+TADjyfb/Ubref7J4XQXhxNvnEV+AJlbTy+XypUqn4KBaLBZuciCa/A0+opN5oNFz7FpUBbP4EHicxxsyAcz7HGDvvz3nar5+2Ho5wOQwsU5+KNGDa+r8grUP0DBLjtRtNKEliAAAAAElFTkSuQmCC"><span id="upText">Top</span></div></div><script src="search.js?v=1694496230674"></script><script>$(function(){const Accordion=function(el,multiple){this.el=el||{};this.multiple=multiple||false;const links=this.el.find(".dd");links.on("click",{el:this.el,multiple:this.multiple},this.dropdown)};Accordion.prototype.dropdown=function(e){const $el=e.data.el;const $this=$(this),$next=$this.next();$next.slideToggle();$this.parent().toggleClass("open");if(!e.data.multiple){$el.find(".submenu").not($next).slideUp("20").parent().removeClass("open")}};new Accordion($("#accordion"),false);hljs.highlightAll();$(window).scroll(function(){if($(window).scrollTop()>100){let $toTop=$("#toTop");$toTop.fadeIn(1500);$toTop.hover(function(){$("#upArrow").hide();$("#upText").show()},function(){$("#upArrow").show();$("#upText").hide()})}else{$("#toTop").fadeOut(1500)}});$("#toTop").click(function(){$("body, html").animate({scrollTop:0},1000);return false})});</script></body></html>