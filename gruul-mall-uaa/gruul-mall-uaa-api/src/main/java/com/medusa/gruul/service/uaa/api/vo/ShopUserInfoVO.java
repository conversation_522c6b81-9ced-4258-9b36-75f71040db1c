package com.medusa.gruul.service.uaa.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2022/10/12
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class ShopUserInfoVO implements Serializable {
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;
}
