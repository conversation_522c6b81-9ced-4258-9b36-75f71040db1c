package com.medusa.gruul.service.uaa.api.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/12
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class UserBaseDataDTO implements Serializable {

    /**
     * 用户 id
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 平台id
     */
    private Long platformId;

    /**
     * 用户手机号
     */
    private String mobile;
}
