package com.medusa.gruul.service.uaa.service.strategy.login;

import cn.hutool.core.collection.CollUtil;

import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.ipaas.model.IpaasUserAndEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.IpassEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.IpassOperatingSubjectBriefDTO;
import com.medusa.gruul.common.member.dto.MemberAddDTO;
import com.medusa.gruul.common.member.dto.MemberQueryDTO;
import com.medusa.gruul.common.model.base.UserKey;
import com.medusa.gruul.common.module.app.shop.ShopStatus;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.model.enums.Roles;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.model.enums.MenuType;
import com.medusa.gruul.common.security.resource.exception.SecurityException;
import com.medusa.gruul.common.security.server.provider.IReloadUserProvider;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.ClientType;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.global.model.constant.SecurityConst;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyOperatingSubjectBriefDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.enums.UaaError;
import com.medusa.gruul.service.uaa.service.model.po.RolesAndPerms;
import com.medusa.gruul.service.uaa.service.mp.entity.Role;
import com.medusa.gruul.service.uaa.service.mp.entity.ShopUserData;
import com.medusa.gruul.service.uaa.service.mp.entity.User;
import com.medusa.gruul.service.uaa.service.mp.entity.UserRole;
import com.medusa.gruul.service.uaa.service.mp.entity.RoleMenu;
import com.medusa.gruul.service.uaa.service.mp.entity.Menu;
import com.medusa.gruul.service.uaa.service.mp.service.IRoleService;
import com.medusa.gruul.service.uaa.service.mp.service.IShopUserDataService;
import com.medusa.gruul.service.uaa.service.mp.service.IUserRoleService;
import com.medusa.gruul.service.uaa.service.mp.service.IUserService;
import com.medusa.gruul.service.uaa.service.mp.service.IRoleMenuService;
import com.medusa.gruul.service.uaa.service.mp.service.IMenuService;
import com.medusa.gruul.service.uaa.service.service.LastLoginService;
import com.medusa.gruul.service.uaa.service.service.UserDataHandlerService;
import com.medusa.gruul.service.uaa.service.strategy.perms.PermsStrategyFactory;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReloadUserProvider implements IReloadUserProvider {

    private final IUserService userService;
    private final ShopRpcService shopRpcService;
    private final LastLoginService lastLoginService;
    private final IUserRoleService userRoleService;
    private final IShopUserDataService shopUserDataService;
    private final IRoleService roleService;
    private final IRoleMenuService roleMenuService;
    private final IMenuService menuService;
    private final PermsStrategyFactory permsStrategyFactory;
    private final UserDataHandlerService userDataHandlerService;

    /**
     * 根据用户id加载用户权限信息
     *
     * @param preUser 加载之前的用户认证资料
     * @return 用户信息
     */
    @Override
    public SecureUser<?> loadUser(SecureUser<?> preUser) {
        User user;
        if (ClientType.CONSUMER == ISystem.clientTypeMust()) {
            //如果是消费者用户，从会员系统查询
            user = userDataHandlerService.getUserFromMember(new MemberQueryDTO().setMemberGuid(String.valueOf(preUser.getId())));
        } else {
            //从商城查询
            user = userService.lambdaQuery().eq(User::getId, preUser.getId()).one();
        }

        //查询角色
        if (user == null) {
            throw SecurityException.of(SecureCodes.ACCOUNT_INVALID);
        }
        return loadUser(preUser.getClientType(), preUser.getShopId(), user);
    }

    /**
     * 根据用户加载用户权限信息
     *
     * @param user 用户信息
     * @return 用户权限信息
     */
    public SecureUser<?> loadUser(ClientType clientType, Long shopId, User user) {
        if (user == null) {
            throw SecurityException.of(SecureCodes.USERNAME_PASSWORD_INVALID);
        }
        Long userId = user.getId();
        Long targetShopId = this.checkClient(userId, clientType, shopId);

        // 拼接店铺信息，后续这里应该是多个店铺
        List<IpassEnterpriseDTO> enterprises = new ArrayList<>();
        List<IpassOperatingSubjectBriefDTO> subjects = new ArrayList<>();
        IpassOperatingSubjectBriefDTO  subjectBriefDTO = new IpassOperatingSubjectBriefDTO().setId(user.getPlatformId());
        subjects.add(subjectBriefDTO);
        IpassEnterpriseDTO ipassEnterpriseDTO = new IpassEnterpriseDTO().setSubjects(subjects);
        enterprises.add(ipassEnterpriseDTO);

        /*
         * 查询用户角色与权限
         */

        UserKey userKey = new UserKey(targetShopId, userId, user.getPlatformId());
        RolesAndPerms rolesAndPerms = ISystem.shopId(
                targetShopId,
                () -> permsStrategyFactory.execute(clientType, userKey)
        );
        //主角色
        Set<Role> roles = rolesAndPerms.getRoles();
        SecureUser<?> secureUser = new SecureUser<>()
                .setClientType(clientType)
                .setShopId(targetShopId)
                .setId(userId)
                .setUsername(user.getUsername())
                .setEmail(user.getEmail())
                .setMobile(user.getMobile())
                .setOpenid(user.getOpenid())
                .setRoles(
                        roles.stream()
                                .map(Role::getValue)
                                .collect(Collectors.toSet())
                )
                .setPerms(rolesAndPerms.getPerms())
                                           .setExtra(enterprises);
        //副角色
        Set<Role> subRoles = rolesAndPerms.getSubRoles();
        if (CollUtil.isNotEmpty(subRoles)) {
            secureUser.setSubRoles(
                    subRoles
                            .stream()
                            .map(Role::getValue)
                            .collect(Collectors.toSet())
            );
        }
        return secureUser;
    }

    /**
     * 通过短信验证码加载iPass用户（重构版本）
     * 简化逻辑，确保跨平台用户身份一致性和权限正确性
     * 
     * @param clientType 客户端类型
     * @param ipaasUserAndEnterprise iPass用户和企业信息
     * @return 安全用户对象
     */
    public SecureUser<List<IpassEnterpriseDTO>> loadIpaasUserBySmsCodeAuth(ClientType clientType, IpaasUserAndEnterpriseDTO ipaasUserAndEnterprise) {
        // 1. 基础参数验证
        validateIpaasUserRequest(ipaasUserAndEnterprise);
        
        // 2. 获取用户基本信息
        String mobile = ipaasUserAndEnterprise.getPhone();
        String username = ipaasUserAndEnterprise.getUsername();
        Long ipaasUserId = ipaasUserAndEnterprise.getUserId();
        
        log.info("开始加载iPass用户，mobile: {}, username: {}, ipaasUserId: {}", mobile, username, ipaasUserId);

        
        // 3. 确保本地用户数据存在（如果不存在则创建）
        ensureLocalUserDataExists(mobile, ipaasUserAndEnterprise.getEnterprises());
        
        // 4. 构建并返回SecureUser（使用iPass统一用户ID和超级管理员权限）
        SecureUser<List<IpassEnterpriseDTO>> secureUser = new SecureUser<List<IpassEnterpriseDTO>>()
                .setClientType(clientType)
                .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID)
                .setId(ipaasUserId)
                .setUsername(username)
                .setMobile(mobile)
                .setExtra(ipaasUserAndEnterprise.getEnterprises())
                .setRoles(Collections.singleton(Roles.SUPER_ADMIN));
        
        log.info("成功构建SecureUser，ipaasUserId: {}, mobile: {}, enterpriseCount: {}",
                ipaasUserId, mobile, ipaasUserAndEnterprise.getEnterprises().size());
        
        return secureUser;
    }
    
    /**
     * 验证iPass用户请求参数
     * 
     * @param ipaasUserAndEnterprise iPass用户和企业信息
     */
    private void validateIpaasUserRequest(IpaasUserAndEnterpriseDTO ipaasUserAndEnterprise) {
        if (ipaasUserAndEnterprise == null) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }
        
        if (CollUtil.isEmpty(ipaasUserAndEnterprise.getEnterprises())) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }
        
        if (ipaasUserAndEnterprise.getUserId() == null) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }
        
        if (StrUtil.isBlank(ipaasUserAndEnterprise.getPhone())) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }
    }
    
    /**
     * 确保本地用户数据存在
     * 如果用户不存在，则自动创建用户和角色关联
     * 
     * @param mobile 手机号
     * @param enterprises 企业列表
     */
    private void ensureLocalUserDataExists(String mobile, List<IpassEnterpriseDTO> enterprises) {
        // 转换企业数据格式
        List<ThirdPartyEnterpriseDTO> thirdPartyEnterprises = enterprises.stream()
                .map(ThirdPartyEnterpriseDTO::transferTo)
                .collect(Collectors.toList());
        
        // 使用统一的用户数据处理逻辑（跨租户查询）
        UserDataProcessResult result = processUserData(mobile, thirdPartyEnterprises);
        
        // 记录处理结果
        if (CollUtil.isNotEmpty(result.newUsers())) {
            log.info("用户不存在，已自动创建用户和角色，mobile: {}, 创建用户数: {}, 处理平台: {}", 
                    mobile, result.newUsers().size(), result.missingPlatformIds());
        } else {
            log.debug("用户已存在，mobile: {}, 现有用户数: {}, 现有平台: {}", 
                    mobile, result.existingUsers().size(), result.processedPlatformIds());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public SecureUser<List<ThirdPartyEnterpriseDTO>> loadThirdPartyUser(ClientType clientType,
                                                                        ThirdPartyUserAndEnterpriseDTO thirdPartyUserAndEnterpriseDTO) {
        // 1. 基础验证
        if (thirdPartyUserAndEnterpriseDTO == null || CollUtil.isEmpty(thirdPartyUserAndEnterpriseDTO.getEnterprises())) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }

        // 2. 获取用户基本信息
        String mobile = thirdPartyUserAndEnterpriseDTO.getPhone();
        String username = thirdPartyUserAndEnterpriseDTO.getUsername();

        // 3. 使用统一的用户数据处理逻辑（当前租户查询）
        UserDataProcessResult result = processUserData(mobile, thirdPartyUserAndEnterpriseDTO.getEnterprises());
        
        // 验证是否有有效的平台ID
        if (CollUtil.isEmpty(result.processedPlatformIds())) {
            log.error("未找到有效的平台信息，enterprises: {}", thirdPartyUserAndEnterpriseDTO.getEnterprises());
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }

        // 获取所有用户（现有的 + 新创建的）
        List<User> allUsers = result.getAllUsers();

        // 4. 构建平台ID到用户的映射关系
        Map<Long, User> platformIdUserMap = allUsers.stream()
                .filter(user -> user.getPlatformId() != null)
                .collect(Collectors.toMap(User::getPlatformId, Function.identity(), (existing, replacement) -> {
                    log.warn("发现重复的平台ID映射，platformId: {}, 保留existing用户ID: {}, 忽略replacement用户ID: {}",
                            existing.getPlatformId(), existing.getId(), replacement.getId());
                    return existing;
                }));

        // 10. 处理角色和权限
        Set<Role> roles = new HashSet<>();
        Set<Role> subRoles = new HashSet<>();
        Set<String> perms = new HashSet<>();

        // 5. 处理企业相关的权限
        List<ThirdPartyEnterpriseDTO> enterprises = thirdPartyUserAndEnterpriseDTO.getEnterprises();
        log.debug("企业信息: enterprises={}", enterprises);
        
        // 主要用户ID，用于构建SecureUser
        Long primaryUserId;
        // 主要平台ID，用于标识主要业务上下文（取第一个平台ID作为主要平台）
        Long primaryPlatformId = ISystem.platformIdMust();
        log.debug("系统上下文platformId: {}", primaryPlatformId);

        for (ThirdPartyEnterpriseDTO enterprise : enterprises) {
            if (CollUtil.isNotEmpty(enterprise.getSubjects())) {
                for (ThirdPartyOperatingSubjectBriefDTO subject : enterprise.getSubjects()) {
                    // 为每个品牌主体设置权限上下文
                    Long subjectPlatformId = subject.getId();

                    // 根据platformId获取对应的用户
                    User platformUser = platformIdUserMap.get(subjectPlatformId);
                    if (platformUser == null) {
                        log.warn("平台用户不存在，mobile: {}, platformId: {}", mobile, subjectPlatformId);
                        continue;
                    }

                    // 使用正确的userId和platformId构建UserKey
                    UserKey userKey = new UserKey(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID, platformUser.getId(), subjectPlatformId);
                    // 显式设置平台ID和shopId到系统上下文，确保多租户拦截器能获取到正确的租户信息
                    RolesAndPerms rolesAndPerms = ISystem.platformId(subjectPlatformId,
                            () -> ISystem.shopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID, () -> {
                                log.debug("设置系统上下文: platformId={}", subjectPlatformId);
                                return permsStrategyFactory.execute(clientType, userKey);
                            }));

                    // 合并角色和权限
                    if (rolesAndPerms != null) {
                        if (CollUtil.isNotEmpty(rolesAndPerms.getRoles())) {
                            roles.addAll(rolesAndPerms.getRoles());
                        }
                        if (CollUtil.isNotEmpty(rolesAndPerms.getSubRoles())) {
                            subRoles.addAll(rolesAndPerms.getSubRoles());
                        }
                        if (CollUtil.isNotEmpty(rolesAndPerms.getPerms())) {
                            perms.addAll(rolesAndPerms.getPerms());
                        }
                    }
                }
            }
        }

        // 如果主要平台ID在用户映射中不存在，则使用第一个可用的平台ID
        if (!platformIdUserMap.containsKey(primaryPlatformId)) {
            log.warn("目标平台{}没有对应用户，使用第一个可用平台。可用平台: {}", 
                     primaryPlatformId, platformIdUserMap.keySet());
            if (!platformIdUserMap.isEmpty()) {
                primaryPlatformId = platformIdUserMap.keySet().iterator().next();
                log.info("切换到可用平台: {}", primaryPlatformId);
            }
        }
        
        primaryUserId = Optional.ofNullable(platformIdUserMap.get(primaryPlatformId)).map(User::getId).orElse(null);
        log.debug("查找主要用户: primaryPlatformId={}, platformIdUserMap.keys={}, primaryUserId={}", 
                primaryPlatformId, platformIdUserMap.keySet(), primaryUserId);
        
        // 6. 确保有主要用户ID
        if (primaryUserId == null) {
            log.error("找不到主要用户: primaryPlatformId={}, 可用的platformIds={}", primaryPlatformId, platformIdUserMap.keySet());
            throw SecurityException.of(SecureCodes.ACCOUNT_INVALID);
        }

        // 7. 构建并返回SecureUser
        SecureUser<List<ThirdPartyEnterpriseDTO>> secureUser = new SecureUser<List<ThirdPartyEnterpriseDTO>>()
                .setClientType(clientType)
                .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID)
                .setId(primaryUserId)
                .setUsername(username)
                .setMobile(mobile)
                .setExtra(enterprises)
                .setRoles(roles.stream().map(Role::getValue).collect(Collectors.toSet()))
                .setPerms(perms);

        // 14. 设置副角色（如果存在）
        if (CollUtil.isNotEmpty(subRoles)) {
            secureUser.setSubRoles(subRoles.stream().map(Role::getValue).collect(Collectors.toSet()));
        }

        log.info("成功构建SecureUser，userId: {}, platformId: {}, mobile: {}, username: {}",
                primaryUserId, primaryPlatformId, mobile, username);

        return secureUser;
    }

    /**
     * 根据平台ID过滤企业信息
     * 
     * @param enterprises 原始企业列表
     * @param targetPlatformIds 目标平台ID集合
     * @return 过滤后的企业列表
     */
    private List<ThirdPartyEnterpriseDTO> filterEnterprisesByPlatformIds(List<ThirdPartyEnterpriseDTO> enterprises, 
                                                                        Set<Long> targetPlatformIds) {
        return enterprises.stream()
                .map(enterprise -> {
                    List<ThirdPartyOperatingSubjectBriefDTO> filteredSubjects = enterprise.getSubjects().stream()
                            .filter(subject -> targetPlatformIds.contains(subject.getId()))
                            .collect(Collectors.toList());
                    
                    if (CollUtil.isNotEmpty(filteredSubjects)) {
                        // 创建新的企业对象，只包含目标平台的主体
                        ThirdPartyEnterpriseDTO filteredEnterprise = new ThirdPartyEnterpriseDTO();
                        BeanUtil.copyProperties(enterprise, filteredEnterprise);
                        filteredEnterprise.setSubjects(filteredSubjects);
                        return filteredEnterprise;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建用户和角色
     *
     * @param mobile 手机号
     * @param enterprises 企业列表
     * @return 创建的用户列表
     */
    private List<User> createUsersAndRoles(String mobile, List<ThirdPartyEnterpriseDTO> enterprises) {
        List<User> createdUsers = new ArrayList<>();

        for (ThirdPartyEnterpriseDTO enterprise : enterprises) {
            // 是否是企业负责人
            boolean isResponsibleUser = enterprise.getResponsibleUserAccount().equals(mobile);
            log.debug("是否为企业负责人: {}", isResponsibleUser);
            if (CollUtil.isNotEmpty(enterprise.getSubjects())) {
                for (ThirdPartyOperatingSubjectBriefDTO subject : enterprise.getSubjects()) {
                    Long platformId = subject.getId();
                    Systems systems = ISystem.systemOpt().getOrNull();
                    systems.setPlatformId(platformId);
                    try {
                        // 创建用户
                        User user = createUserForPlatform(mobile, platformId);
                        createdUsers.add(user);
                        log.info("成功创建用户,userId:{}", user.getId());
                        // 只有企业负责人才去创建角色
                        if (isResponsibleUser) {
                            // 创建或获取角色
                            Role role = createOrGetRole(platformId);
                            // 创建用户角色关联
                            createUserRoleAndRoleMenu(user.getId(), role.getId(), platformId);
                            // 创建管理员数据
                            createShopUserData(user);
                            log.info("成功创建角色， roleId: {}, platformId: {}", role.getId(),
                                    platformId);
                        } else {
                            // 非企业负责人：创建基础用户角色
                            Role basicRole = ISystem.platformId(platformId, this::createOrGetBasicUserRole);
                            createUserRoleAndRoleMenu(user.getId(), basicRole.getId(), platformId);
                            log.info("成功创建基础用户角色， roleId: {}, platformId: {}", basicRole.getId(),
                                    platformId);
                        }

                    } catch (Exception e) {
                        log.error("创建用户和角色失败，mobile: {}, platformId: {}, error: {}", mobile, platformId, e.getMessage(), e);
                        throw new RuntimeException("创建用户失败", e);
                    } finally {
                        // 清除系统上下文中的平台ID
                        systems.setPlatformId(null);
                    }
                }
            }
        }

        return createdUsers;
    }

    private void createShopUserData (User user) {

        ShopUserData shopUserData = new ShopUserData()
                                            .setNickname(user.getUsername())
                                            .setUserId(user.getId())
                                            .setMobile(user.getMobile())
                               .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID);
        boolean success = shopUserDataService.save(shopUserData);
        if (!success) {
            throw new RuntimeException("创建管理员数据失败");
        }
    }

    /**
     * 为指定平台创建用户
     */
    private User createUserForPlatform(String mobile, Long platformId) {
        // 使用现有的用户创建逻辑
        MemberAddDTO addDTO = new MemberAddDTO()
                .setPhoneNum(mobile);

        User user = userDataHandlerService.addUserToMember(addDTO);
        
        // 设置平台ID
        user.setUsername("系统默认创建用户" + "_" + StrUtil.subSufByLength(mobile, 6));
        user.setPlatformId(platformId);
        userService.updateById(user);
        
        return user;
    }

    /**
     * 创建或获取基础用户角色 -- 平台子超级管理员
     */
    private Role createOrGetBasicUserRole() {
        // 查找是否已存在基础用户角色 -- 子管理员
        Role existingRole = roleService.lambdaQuery()
                                    .eq(Role::getValue, Roles.SUPER_CUSTOM_ADMIN)
                                    .eq(Role::getName, Roles.SUPER_CUSTOM_ADMIN.getDesc())
                                    .one();

        if (existingRole != null) {
            log.debug("基础用户角色已存在，roleId: {}, platformId: {}", existingRole.getId(), ISystem.platformIdOpt());
            return existingRole;
        }

        // 创建基础用户角色
        Role basicRole = new Role()
                                 .setValue(Roles.SUPER_CUSTOM_ADMIN)
                                 .setName("子超级管理员");

        roleService.save(basicRole);
        log.info("成功创建基础用户角色，roleId: {}, platformId: {}, roleType: {}",
                basicRole.getId(), ISystem.platformIdOpt(), basicRole.getValue());

        return basicRole;
    }

    /**
     * 创建或获取角色
     */
    private Role createOrGetRole(Long platformId) {
        // 确定目标角色类型
        Roles targetRole = Roles.SUPER_ADMIN;
        
        // 查询角色是否已存在
        Role existingRole = roleService.lambdaQuery()
                .eq(Role::getPlatformId, platformId)
                .eq(Role::getValue, targetRole)
                                    .eq(Role::getName,targetRole.getDesc())
                .eq(Role::getDeleted, Boolean.FALSE)
                .one();
        
        if (existingRole != null) {
            log.debug("角色已存在，roleId: {}, platformId: {}, roleType: {}", existingRole.getId(), platformId, targetRole);
            return existingRole;
        }
        
        // 创建新角色
        Role newRole = new Role()
                .setPlatformId(platformId)
                .setValue(targetRole)
                .setName(targetRole.getDesc())
                .setClientType(targetRole.getClientType())
                .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID);
        
        boolean success = roleService.save(newRole);
        if (!success) {
            throw new RuntimeException("创建角色失败");
        }
        
        log.info("成功创建角色，roleId: {}, platformId: {}, roleType: {}", newRole.getId(), platformId, targetRole);
        return newRole;
    }

    /**
     * 创建用户角色关联
     */
    private void createUserRoleAndRoleMenu (Long userId, Long roleId, Long platformId) {
        UserRole userRole = new UserRole()
                .setUserId(userId)
                .setRoleId(roleId)
                .setPlatformId(platformId)
                .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID)
                .setEnable(Boolean.TRUE);
        
        boolean success = userRoleService.save(userRole);
        if (!success) {
            throw new RuntimeException("创建用户角色关联失败");
        }
        
        log.debug("成功创建用户角色关联，userId: {}, roleId: {}, platformId: {}", userId, roleId, platformId);
        
        // 处理角色菜单关联
        handleRoleMenuAssociation(roleId);
    }

    /**
     * 处理角色菜单关联
     * 如果角色没有菜单权限，则分配默认的平台控制台菜单
     * 
     * @param roleId 角色ID
     */
    private void handleRoleMenuAssociation(Long roleId) {
        // 检查角色是否已有菜单权限
        long existingMenuCount = roleMenuService.lambdaQuery()
                .eq(RoleMenu::getRoleId, roleId)
                .count();
        
        if (existingMenuCount == 0) {
            // 角色没有菜单权限，分配默认菜单
            assignDefaultMenusToRole(roleId);
        } else {
            log.debug("角色 [{}] 已有 [{}] 个菜单权限，跳过默认菜单分配", roleId, existingMenuCount);
        }
    }

    /**
     * 为角色分配默认菜单权限
     * 
     * @param roleId 角色ID
     */
    private void assignDefaultMenusToRole(Long roleId) {
        // 查询平台控制台的所有菜单（type=1, client_type=0, deleted=0）
        List<Menu> defaultMenus = menuService.lambdaQuery()
                .eq(Menu::getType, MenuType.MENU.getValue())
                .eq(Menu::getClientType, ClientType.PLATFORM_CONSOLE.getValue())
                .eq(Menu::getDeleted, Boolean.FALSE)
                .list();
        // 客服消息菜单
        Menu messageCustomerServiceMenu = menuService.lambdaQuery()
                .eq(Menu::getType, MenuType.MENU.getValue())
                .eq(Menu::getDeleted, Boolean.FALSE)
                .eq(Menu::getPath, "/message/customer/service")
                .one();
        if (messageCustomerServiceMenu != null) {
            defaultMenus.add(messageCustomerServiceMenu);
        }
        if (CollUtil.isNotEmpty(defaultMenus)) {
            // 构建角色菜单关联数据
            List<RoleMenu> roleMenuList = defaultMenus.stream()
                    .map(menu -> new RoleMenu()
                            .setRoleId(roleId)
                            .setMenuId(menu.getId()))
                    .collect(Collectors.toList());
            
            // 批量保存角色菜单关联
            boolean success = roleMenuService.saveBatch(roleMenuList);
            if (!success) {
                throw new RuntimeException("分配默认菜单权限失败，roleId: " + roleId);
            }
            
            log.info("为角色 [{}] 分配了 [{}] 个默认菜单权限", roleId, roleMenuList.size());
        } else {
            log.warn("未找到可分配的默认菜单，roleId: {}", roleId);
        }
    }

    /**
     * 用户数据处理结果
     * @param existingUsers
     * @param newUsers
     * @param processedPlatformIds
     * @param missingPlatformIds
     */
    private record UserDataProcessResult(List<User> existingUsers, List<User> newUsers,
                                         Set<Long> processedPlatformIds,
                                         Set<Long> missingPlatformIds) {
        private UserDataProcessResult (List<User> existingUsers, List<User> newUsers,
                                       Set<Long> processedPlatformIds,
                                       Set<Long> missingPlatformIds) {
            this.existingUsers = existingUsers != null ? existingUsers : new ArrayList<>();
            this.newUsers = newUsers != null ? newUsers : new ArrayList<>();
            this.processedPlatformIds = processedPlatformIds != null ? processedPlatformIds :
                                                new HashSet<>();
            this.missingPlatformIds = missingPlatformIds != null ? missingPlatformIds :
                                              new HashSet<>();
        }

        public List<User> getAllUsers () {
            List<User> allUsers = new ArrayList<>(existingUsers);
            allUsers.addAll(newUsers);
            return allUsers;
        }
    }

    /**
     * 核心用户数据处理逻辑
     * 统一处理用户查询、平台分析、用户创建的通用逻辑
     *
     * @param mobile 手机号
     * @param enterprises 企业列表
     * @return 用户数据处理结果
     */
    private UserDataProcessResult processUserData(String mobile,
                                                 List<ThirdPartyEnterpriseDTO> enterprises) {
        // 1. 查询已存在的用户
        List<User> existingUsers = queryUsersByMobile(mobile);

        log.debug("查询到的企业信息列表:{}", JSON.toJSONString(enterprises));
        // 2. 提取平台ID信息
        Set<Long> requiredPlatformIds = extractPlatformIds(enterprises);
        Set<Long> existingPlatformIds = extractExistingPlatformIds(existingUsers);
        
        // 3. 找出需要创建用户的平台ID
        Set<Long> missingPlatformIds = requiredPlatformIds.stream()
                .filter(requiredId -> !existingPlatformIds.contains(requiredId))
                .collect(Collectors.toSet());
        
        // 4. 为缺失的平台创建用户
        List<User> newUsers = new ArrayList<>();
        if (CollUtil.isNotEmpty(missingPlatformIds)) {
            log.info("需要为平台创建用户，mobile: {}, missingPlatformIds: {}", mobile, missingPlatformIds);
            
            // 构建缺失平台的企业信息
            List<ThirdPartyEnterpriseDTO> missingEnterprises = filterEnterprisesByPlatformIds(enterprises, missingPlatformIds);
            newUsers = createUsersAndRoles(mobile, missingEnterprises);
        } else {
            log.debug("所有平台的用户都已存在，mobile: {}, existingPlatformIds: {}", mobile, existingPlatformIds);
        }
        
        return new UserDataProcessResult(existingUsers, newUsers, requiredPlatformIds, missingPlatformIds);
    }

    /**
     * 按手机号查询用户
     */
    private List<User> queryUsersByMobile(String mobile) {
        return Tenant.disable(() -> userService.lambdaQuery()
                                            .eq(User::getDeleted, Boolean.FALSE)
                                            .eq(User::getMobile, mobile)
                                            .list());
    }

    /**
     * 从企业列表中提取平台ID
     */
    private Set<Long> extractPlatformIds(List<ThirdPartyEnterpriseDTO> enterprises) {
        return enterprises.stream()
                .flatMap(enterprise -> enterprise.getSubjects().stream())
                .map(ThirdPartyOperatingSubjectBriefDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 从已存在的用户列表中提取平台ID
     */
    private Set<Long> extractExistingPlatformIds(List<User> existingUsers) {
        return existingUsers.stream()
                .map(User::getPlatformId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 检查请求参数
     */
    private Long checkClient(Long userId, ClientType clientType, Long shopId) {
        if (clientType.isSwitchShop() && shopId == null) {
            shopId = this.lastLoginService.getLastLoginShopId(clientType, userId);
        }
        if (shopId == null) {
            throw SecureCodes.USERNAME_PASSWORD_INVALID.exception();
        }
        /*
         * 如果需要检查店铺状态则检查店铺是否可用
         */
        if (clientType.isCheckShop()) {
            ShopInfoVO shopInfo = this.shopRpcService.getShopInfoByShopId(shopId);
            if (shopInfo == null || ShopStatus.NORMAL != shopInfo.getStatus()) {
                lastLoginService.removeLastLoginShopId(clientType, userId);
                return checkClient(userId, clientType, null);
            }
            return shopId;
        }
        //只能是平台 或 消费者端登录  店铺 id 只能是 0
        SecureCodes.REQUEST_INVALID.falseThrow(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID == shopId);
        return shopId;
    }

    public SecureUser<?> loadShopAdminUser (ClientType clientType, List<IpassOperatingSubjectBriefDTO> subjectBriefDTOS, List<User> users) {
        // 1. 基础验证
        if (CollUtil.isEmpty(users)) {
            throw SecurityException.of(SecureCodes.DATA_VALID_ERROR);
        }

        // 2. 获取有效的用户角色
        List<Long> userIds = users.stream().map(User::getId).toList();

        final List<Long> roleUserIdList =
                Optional.ofNullable(TenantShop.disable(()->userRoleService.lambdaQuery()
                                            .select(UserRole::getUserId).in(UserRole::getUserId, userIds)
                                                                   .eq(UserRole::getEnable,Boolean.TRUE)
                                                                   .ne(UserRole::getShopId,0)
                                                                   .list())).orElse(Collections.emptyList())
                        .stream().map(UserRole::getUserId).collect(Collectors.toList());

        // 3. 验证用户是否有商家角色
        boolean allMallUserIdsInList =
                subjectBriefDTOS.stream().anyMatch(sub -> roleUserIdList.contains(sub.getMallUserId()));
        if (CollUtil.isEmpty(roleUserIdList) || !allMallUserIdsInList) {
            throw UaaError.MERCHANDISER_NOT_EXIST.exception();
        }

        // 4. 处理店铺信息和权限
        Set<Role> roles = new HashSet<>();
        Set<Role> subRoles = new HashSet<>();
        Set<String> perms = new HashSet<>();
        Set<Long> targetShopIds = new HashSet<>();
        List<IpassOperatingSubjectBriefDTO> extra = new ArrayList<>();

        // 5. 获取最后登录的店铺ID(用于设置默认shopId)
        Long primaryUserId =
                users.stream().filter(user -> roleUserIdList.contains(user.getId())).findFirst().map(User::getId).orElseThrow(() -> SecurityException.of(SecureCodes.DATA_VALID_ERROR));

        Long lastLoginShopId = lastLoginService.getLastLoginShopId(clientType, primaryUserId);

        // 6. 构建用户ID到用户对象的映射，用于获取平台ID
        Map<Long, User> userIdMap = users.stream()
                .collect(Collectors.toMap(User::getId, user -> user));

        // 7. 处理每个店铺的权限
        for (IpassOperatingSubjectBriefDTO sub : subjectBriefDTOS) {
            // 只处理有商家角色的用户
            if (!roleUserIdList.contains(sub.getMallUserId())) {
                continue;
            }

            // 获取店铺ID
            Long targetShopId;
            try {
                targetShopId = this.checkClient(sub.getMallUserId(), clientType, null);
            } catch (Exception e) {
                log.warn("loadShopAdminUser - 检查店铺失败，userId: {}, error: {}", sub.getMallUserId(), e.getMessage());
                continue;
            }
            // 验证店铺状态
            ShopInfoVO shopInfo = shopRpcService.getShopInfoByShopId(targetShopId);

            // 跳过非正常状态的店铺
            if (shopInfo == null || ShopStatus.NORMAL != shopInfo.getStatus()) {
                continue;
            }

            sub.setMallShopId(targetShopId);
            targetShopIds.add(targetShopId);

            // 🎯 获取用户的平台ID并显式设置到系统上下文
            User currentUser = userIdMap.get(sub.getMallUserId());
            Long platformId = currentUser != null ? currentUser.getPlatformId() : null;

            // 获取店铺的角色和权限
            UserKey userKey = new UserKey(targetShopId, sub.getMallUserId(), platformId);
            
            RolesAndPerms rolesAndPerms = ISystem.shopId(targetShopId,
                    () -> {
                        // 🎯 在执行权限策略之前，确保系统上下文中有正确的平台ID
                        if (platformId != null) {
                            Systems currentSystems = ISystem.systemOpt().getOrNull();
                            if (currentSystems != null && currentSystems.getPlatformId() == null) {
                                currentSystems.setPlatformId(platformId);
                                currentSystems.setShopId(targetShopId);
                                log.debug("loadShopAdminUser - 显式设置平台ID到系统上下文: {}", platformId);
                            }
                        }
                        return permsStrategyFactory.execute(clientType, userKey);
                    });

            roles.addAll(rolesAndPerms.getRoles());
            subRoles.addAll(rolesAndPerms.getSubRoles());
            perms.addAll(rolesAndPerms.getPerms());
            extra.add(sub);
        }

        // 8. 如果没有任何可用店铺,抛出异常
        if (targetShopIds.isEmpty()) {
            throw UaaError.MERCHANDISER_NOT_EXIST.exception();
        }

        // 9. 确定当前使用的shopId
        Long currentShopId;
        if (lastLoginShopId != null && targetShopIds.contains(lastLoginShopId)) {
            // 优先使用最后登录的店铺
            currentShopId = lastLoginShopId;
        } else {
            // 否则使用第一个可用店铺
            currentShopId = targetShopIds.iterator().next();
        }

        // 10. 构建并返回SecureUser
        return new SecureUser<List<IpassOperatingSubjectBriefDTO>>()
                       .setId(primaryUserId)
                       .setClientType(clientType)
                       .setShopId(currentShopId)
                       .setShopIds(new ArrayList<>(targetShopIds))
                       .setRoles(roles.stream().map(Role::getValue).collect(Collectors.toSet()))
                       .setSubRoles(subRoles.stream().map(Role::getValue).collect(Collectors.toSet()))
                       .setPerms(perms).setExtra(extra);
    }
}

