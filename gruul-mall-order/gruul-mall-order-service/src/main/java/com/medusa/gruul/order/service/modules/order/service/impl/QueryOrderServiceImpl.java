package com.medusa.gruul.order.service.modules.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.ClientType;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderPayment;
import com.medusa.gruul.order.api.entity.OrderReceiver;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.OrderStatus;
import com.medusa.gruul.order.api.model.ic.ICStatus;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.order.service.model.bo.OrderQueryBO;
import com.medusa.gruul.order.service.model.bo.ShopOrderPackageQueryBO;
import com.medusa.gruul.order.service.model.bo.ShopOrderQueryBO;
import com.medusa.gruul.order.service.model.dto.OrderCountQueryDTO;
import com.medusa.gruul.order.service.model.dto.OrderDetailQueryDTO;
import com.medusa.gruul.order.service.model.enums.OrderError;
import com.medusa.gruul.order.service.model.vo.BuyerOrderCountVO;
import com.medusa.gruul.order.service.model.vo.OrderCountVO;
import com.medusa.gruul.order.service.modules.order.addon.OrderAddonDistributionSupporter;
import com.medusa.gruul.order.service.modules.order.service.QueryOrderService;
import com.medusa.gruul.order.service.mp.service.*;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.user.api.model.UserDataVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;

import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 订单查询服务实现类
 *
 * <AUTHOR>
 * date 2022/6/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryOrderServiceImpl implements QueryOrderService {

    private final Executor globalExecutor;
    private final IOrderService orderService;
    private final OrderAddonDistributionSupporter orderAddonDistributionSupporter;
    private final IOrderDiscountService orderDiscountService;
    private final IShopOrderItemService shopOrderItemService;
    private final IOrderPaymentService orderPaymentService;
    private final IShopOrderPackageService shopOrderPackageService;
    private final IOrderReceiverService orderReceiverService;
    private final ShopRpcService shopRpcService;
    private final UserRpcService userRpcService;


    @Override
    public Option<Order> orderDetail(OrderDetailQueryDTO orderDetailQuery) {
        OrderQueryBO query = new OrderQueryBO().setOrderNo(orderDetailQuery.getOrderNo())
                .setShopOrderShopId(orderDetailQuery.getShopId())
                .setUsePackage(BooleanUtil.isTrue(orderDetailQuery.getUsePackage()))
                .setPackageId(orderDetailQuery.getPackageId());
        ISecurity.match()
                .ifAnySupplierAdmin(secureUser -> query.setShopOrderSupplierId(secureUser.getShopId()).setSellType(SellType.CONSIGNMENT))
                .ifAnyShopAdmin(secureUser -> query.setShopOrderShopId(secureUser.getShopId()))
                .ifUser(secureUser -> query.setBuyerId(secureUser.getId()));

        Order order = TenantShop.disable(() -> orderService.getOrder(query));
        dealRemark(order);
        
        // 设置shopType信息
        setShopTypeForOrder(order);
        
        return Option.of(order)
                .peek(ord -> {
                    String orderNo = query.getOrderNo();
                    CompletableTask.getOrThrowException(
                            CompletableTask.allOf(
                                    globalExecutor,
                                    () -> ord.setOrderDiscounts(
                                            TenantShop.disable(() -> orderDiscountService.orderDiscounts(orderDetailQuery))
                                    ),
                                    () -> {
                                        if (DistributionMode.INTRA_CITY_DISTRIBUTION == order.getDistributionMode()) {
                                            ICStatus icStatus = MapUtil.emptyIfNull(orderAddonDistributionSupporter.icOrderStatus(Set.of(orderNo), false)).get(orderNo);
                                            if (icStatus != null) {
                                                order.setIcStatus(icStatus.getStatus());
                                                order.setIcStatusDesc(icStatus.getStatusDesc());
                                            }
                                        }
                                        if (BooleanUtil.isTrue(orderDetailQuery.getUsePackage()) && query.getPackageId() == null) {
                                            return;
                                        }
                                        ord.setShopOrderPackages(
                                                TenantShop.disable(() -> shopOrderPackageService.deliveredPackages(
                                                        orderNo,
                                                        new ShopOrderPackageQueryBO()
                                                                .setShopId(query.getShopOrderShopId())
                                                                .setBuyerId(query.getBuyerId())
                                                                .setLimitOne(Boolean.FALSE)
                                                                .setPackageId(query.getPackageId())

                                                ))

                                        );

                                    },
                                    () -> ord.setBuyerPhone(Option.of(userRpcService.userData(ord.getBuyerId())).map(UserDataVO::getMobile).getOrNull())
                            )
                    );
                });
    }

    private static void dealRemark(Order order) {
        if (Objects.nonNull(order)) {
            order.getShopOrders().forEach(shopOrder -> {
                JSONObject jsonObject = JSON.parseObject(shopOrder.getRemark());
                //不显示 平台备注 商家  供应商
                jsonObject.remove(OrderConstant.PLATFORM_REMARK_KEY);
                jsonObject.remove(OrderConstant.SHOP_REMARK_KEY);
                jsonObject.remove(OrderConstant.SUPPLIER_REMARK_KEY);
                shopOrder.setRemark(jsonObject.toJSONString());
            });
        }
    }

    @Override
    public Option<ShopOrder> getShopOrderByNo(String orderNo, String shopOrderNo) {
        ShopOrderQueryBO query = new ShopOrderQueryBO()
                .setOrderNos(Collections.singleton(orderNo))
                .setShopOrderNo(shopOrderNo);
        ISecurity.match()
                .ifUser(secureUser -> query.setBuyerId(secureUser.getId()))
                .ifAnyShopAdmin(secureUser -> query.setShopIds(Set.of(secureUser.getShopId())))
                .ifAnySupplierAdmin(
                        secureUser -> query.setSupplierId(secureUser.getShopId())
                                .setSellTypes(Set.of(SellType.CONSIGNMENT))
                );
        List<ShopOrder> shopOrders = TenantShop.disable(() -> orderService.getShopOrders(query));
        return Option.when(CollUtil.isNotEmpty(shopOrders), () -> shopOrders.get(0));
    }

    @Override
    public OrderPayment getUnpaidOrderPayment(String orderNo) {
        Order order = orderService.lambdaQuery().eq(Order::getNo, orderNo).one();
        if (order == null) {
            throw OrderError.ORDER_NOT_EXIST.exception();
        }
        OrderStatus orderStatus = order.getStatus();
        if (OrderStatus.PAID == orderStatus) {
            throw OrderError.ORDER_PAID.exception();
        }
        if (OrderStatus.UNPAID != orderStatus) {
            throw OrderError.ORDER_CANNOT_PAY.exception();
        }
        OrderPayment orderPayment = orderPaymentService.lambdaQuery()
                .eq(OrderPayment::getOrderNo, orderNo)
                .one();
        if (orderPayment == null) {
            throw OrderError.ORDER_CANNOT_PAY.exception();
        }
        return orderPayment.setOrder(order);
    }

    @Override
    public OrderPayment getUnpaidOrderPaymentZero(String orderNo) {
        Order order = orderService.lambdaQuery().eq(Order::getNo, orderNo).one();
        if (order == null) {
            throw OrderError.ORDER_NOT_EXIST.exception();
        }
        OrderPayment orderPayment = orderPaymentService.lambdaQuery()
                .eq(OrderPayment::getOrderNo, orderNo)
                .one();
        if (orderPayment == null) {
            throw OrderError.ORDER_CANNOT_PAY.exception();
        }
        return orderPayment.setOrder(order);
    }

    @Override
    public BuyerOrderCountVO buyerOrderCount() {
        return TenantShop.disable(() -> orderService.buyerOrderCount(ISecurity.userMust().getId()));
    }

    @Override
    public boolean orderCreation(String orderNo) {
        return BooleanUtil.isFalse(
                RedisUtil.getRedisTemplate()
                        .hasKey(RedisUtil.key(OrderConstant.ORDER_CACHE_KEY, ISecurity.userMust().getId(), orderNo))
        );
    }

    @Override
    public Option<ShopOrderItem> getShopOrderItem(String orderNo, Long itemId) {
        return Option.of(
                TenantShop.disable(
                        () -> shopOrderItemService.lambdaQuery()
                                .eq(ShopOrderItem::getOrderNo, orderNo)
                                .eq(ShopOrderItem::getId, itemId)
                                .one()
                )
        );
    }

    @Override
    public OrderCountVO orderCount(OrderCountQueryDTO query) {
        OrderCountVO count = TenantShop.disable(() -> orderService.orderCount(query));
        return count == null ? new OrderCountVO() : count;
    }

    @Override
    public Map<String, OrderInfo> batchOrderDetailsWithPayment(Set<String> orderNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return Map.of();
        }

        // 批量查询订单详情
        Map<String, Order> orderMap = orderService.lambdaQuery()
                .in(Order::getNo, orderNos)
                .list()
                .stream()
                .collect(Collectors.toMap(Order::getNo, Function.identity()));

        if (orderMap.isEmpty()) {
            return Map.of();
        }

        // 批量查询订单收货人信息
        Map<String, OrderReceiver> receiverMap = orderReceiverService.lambdaQuery()
                .in(OrderReceiver::getOrderNo, orderNos)
                .list()
                .stream()
                .collect(Collectors.toMap(OrderReceiver::getOrderNo, Function.identity()));

        // 批量查询支付信息
        Map<String, OrderPayment> paymentMap = orderPaymentService.lambdaQuery()
                .in(OrderPayment::getOrderNo, orderNos)
                .list()
                .stream()
                .collect(Collectors.toMap(OrderPayment::getOrderNo, Function.identity()));

        // 组装OrderInfo
        return orderNos.stream()
                .map(orderNo -> {
                    Order order = orderMap.get(orderNo);
                    if (order == null) {
                        return null;
                    }
                    // 设置收货人信息
                    order.setOrderReceiver(receiverMap.get(orderNo));

                    OrderInfo orderInfo = new OrderInfo();
                    orderInfo.setOrder(order);
                    orderInfo.setPayment(paymentMap.get(orderNo));
                    orderInfo.setOrderNo(orderNo);
                    orderInfo.setBuyerId(order.getBuyerId());
                    return orderInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OrderInfo::getOrderNo, Function.identity()));
    }

    /**
     * 为订单设置shopType - 简化版实现
     */
    private void setShopTypeForOrder(Order order) {
        if (order == null || CollUtil.isEmpty(order.getShopOrders())) {
            return;
        }
        
        try {
            Set<Long> shopIds = order.getShopOrders().stream()
                    .map(ShopOrder::getShopId)
                    .collect(Collectors.toSet());
                    
            List<ShopInfoVO> shopInfoList = shopRpcService.getShopInfoByShopIdList(shopIds);
            Map<Long, ShopInfoVO> shopInfoMap = shopInfoList.stream()
                    .collect(Collectors.toMap(ShopInfoVO::getId, Function.identity()));
            
            // 为每个shopOrder设置shopType    
            order.getShopOrders().forEach(shopOrder -> {
                ShopInfoVO shopInfo = shopInfoMap.get(shopOrder.getShopId());
                if (shopInfo != null) {
                    shopOrder.setShopType(shopInfo.getShopType());
                }
            });
        } catch (Exception e) {
            // 设置shopType失败不影响主流程
            log.warn("设置shopType失败, orderNo: {}, 错误: {}", order.getNo(), e.getMessage());
        }
    }
}