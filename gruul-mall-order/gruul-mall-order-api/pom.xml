<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.medusa.gruul</groupId>
        <artifactId>gruul-mall-order</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-mall-order-api</artifactId>
    <version>1.0</version>
    <dependencies>
        
        
        <dependency>
            <artifactId>gruul-common-module-api</artifactId>
            <groupId>com.medusa.gruul</groupId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-common-afs</artifactId>
        </dependency>

        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-common-shop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-wechat</artifactId>
        </dependency>

        <!--   物理几何     -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-geometry</artifactId>
        </dependency>

        <!--     test   -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- ipaas -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-ipaas</artifactId>
        </dependency>
    </dependencies>

</project>
