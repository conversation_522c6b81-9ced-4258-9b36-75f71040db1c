package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.global.model.o.FormInput;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 下单 表单设置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "t_order_form", autoResultMap = true)
public class OrderForm extends BaseEntity {
    /**
     * 商家注册信息id
     */
    private Long shopId;

    /**
     * 是否开启下单通知：0->关闭；1->开启
     */
    @NotNull
    private Boolean orderNotify;

    /**
     * 自定义表单
     */
    @Valid
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<FormInput> customFrom;
}
