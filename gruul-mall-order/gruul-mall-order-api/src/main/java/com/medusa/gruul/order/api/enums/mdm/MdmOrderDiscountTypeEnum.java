package com.medusa.gruul.order.api.enums.mdm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.medusa.gruul.order.api.enums.DiscountSourceType;

/**
 * MDM订单优惠类型枚举
 */
@Getter
@AllArgsConstructor
public enum MdmOrderDiscountTypeEnum {
    /**
     * 系统抹零
     */
    SYSTEM(0, "系统抹零"),

    /**
     * 整单让价
     */
    ORDER_DISCOUNT(1, "整单让价"),

    /**
     * 单品改价
     */
    ITEM_DISCOUNT(2, "单品改价"),

    /**
     * 会员优惠
     */
    MEMBER_DISCOUNT(3, "会员优惠"),

    /**
     * 优惠卷
     */
    COUPON(4, "优惠卷"),

    /**
     * 限时特价
     */
    LIMIT_SPECIAL(5, "限时特价"),

    /**
     * 单品折扣
     */
    ITEM_PERCENT_DISCOUNT(6, "单品折扣"),

    /**
     * 整单折扣
     */
    ORDER_PERCENT_DISCOUNT(7, "整单折扣"),

    /**
     * 积分抵现
     */
    INTEGRAL_CASH(8, "积分抵现"),

    /**
     * 满减满折活动
     */
    FULL_REDUCTION_AND_DISCOUNT(20, "满减满折活动"),

    /**
     * 平台优惠券
     */
    PLATFORM_COUPON(100, "平台优惠券"),

    /**
     * 店铺优惠券
     */
    SHOP_COUPON(101, "店铺优惠券"),

    /**
     * 会员抵扣
     */
    MEMBER_DEDUCTION(102, "会员抵扣"),

    /**
     * 满减
     */
    FULL_REDUCTION(103, "满减"),

    /**
     * 消费返利
     */
    CONSUMPTION_REBATE(104, "消费返利");

    private final int value;
    private final String description;

    public static MdmOrderDiscountTypeEnum fromDiscountSourceType(DiscountSourceType sourceType) {
        if (sourceType == null) {
            return null;
        }
        return switch (sourceType) {
            case PLATFORM_COUPON -> PLATFORM_COUPON;
            case SHOP_COUPON -> SHOP_COUPON;
            case MEMBER_DEDUCTION -> MEMBER_DEDUCTION;
            case FULL_REDUCTION -> FULL_REDUCTION;
            case CONSUMPTION_REBATE -> CONSUMPTION_REBATE;
            case COUPON_EXCHANGE -> COUPON; // 兑换券映射到优惠券
        };
    }
} 