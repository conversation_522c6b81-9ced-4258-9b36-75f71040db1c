package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.common.module.app.shop.ShopType;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.order.api.enums.ShopOrderStatus;
import com.medusa.gruul.order.api.model.ShopOrderFormModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 店铺订单表
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "t_shop_order", autoResultMap = true)
@NoArgsConstructor
public class ShopOrder extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 店铺订单号
     * {主订单号}-{序号}
     */
    @TableField("`no`")
    private String no;
    /**
     * 店铺订单状态
     */
    @TableField("`status`")
    private ShopOrderStatus status;
    /**
     * 店铺id
     */
    private Long shopId;

    private Long platformId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String shopLogo;

    /**
     * 店铺订单实际总额
     */
    private Long totalAmount;

    /**
     * 店铺订单实际运费总额
     */
    private Long freightAmount;

    /**
     * 店铺订单折扣总额
     */
    private Long discountAmount;

    /**
     * 店铺订单备注
     */
    private String remark;

    /**
     * 额外信息
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private Extra extra;

    /* ** 以下为业务处理代码 ** */
    /**
     * 订单收货人
     */
    @TableField(exist = false)
    private OrderReceiver orderReceiver;

    /**
     * 店铺订单项
     */
    @TableField(exist = false)
    private List<ShopOrderItem> shopOrderItems;
    /**
     * 店铺订单项
     * 根据skuId分组
     * 将同一个sku的订单项合并到同一个skuGroupShopOrderItems中
     * 属于上面拆单的逆操作
     */
    @TableField(exist = false)
    private Map<Long, List<ShopOrderItem>> skuGroupShopOrderItems;

    /**
     * 关联订单信息
     */
    @ToString.Exclude
    @TableField(exist = false)
    private Order order;

    /**
     * 店铺类型
     */
    @TableField(exist = false)
    private ShopType shopType;

    /**
     * 店铺运营模式
     */
    @TableField(exist = false)
    private ShopMode shopMode;

    public ShopOrder(ShopOrder t) {
        this.no = t.getNo();
        this.status = t.getStatus();
        this.shopId = t.getShopId();
        this.orderNo = t.getOrderNo();
        this.shopName = t.getShopName();
        this.shopType = t.getShopType();
        this.shopLogo = t.getShopLogo();
        this.totalAmount = t.getTotalAmount();
        this.freightAmount = t.getFreightAmount();
        this.discountAmount = t.getDiscountAmount();
        this.remark = t.getRemark();
        this.extra = t.getExtra();
        this.orderReceiver = t.getOrderReceiver();
        this.shopOrderItems = t.getShopOrderItems();
        this.order = t.getOrder();
    }


    public Long payAmount() {
        long total = getTotalAmount();
        total = total < 0 ? 0 : total;
        long freight = getFreightAmount();
        freight = freight < 0 ? 0 : freight;
        long discount = getDiscountAmount();
        discount = discount < 0 ? 0 : discount;
        return Math.max(0, total - discount) + freight;
    }

    /**
     * 只获取备注信息 不获取其它表单信息
     *
     * @return 备注信息
     */
    public String remarkOnly() {
        return ShopOrderFormModel.remarkFirst(remark);
    }

    /**
     * 店铺订单额外信息
     */
    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class Extra implements Serializable {
        /**
         * 初始化的空对象
         */
        public static final Extra EMPTY = new Extra();

        /**
         * 取件码
         */
        private Long pickupCode;

        /**
         * 是否可确认收货
         */
        private Boolean icReceivable;

        /**
         * 发货时间
         */
        private LocalDateTime deliverTime;

        /**
         * 可确认收货时间
         */
        private LocalDateTime receivableTime;

        /**
         * 收货时间
         */
        private LocalDateTime receiveTime;


    }

}
