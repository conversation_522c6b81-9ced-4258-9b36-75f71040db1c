package com.medusa.gruul.order.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 产生的优惠项的类型
 *
 * <AUTHOR>
 * date 2022/6/8
 */
@Getter
@RequiredArgsConstructor
public enum DiscountSourceType {
    /**
     * 平台优惠券
     */
    PLATFORM_COUPON(0, Boolean.TRUE),

    /**
     * 店铺优惠券
     */
    SHOP_COUPON(1, Boolean.FALSE),

    /**
     * 会员抵扣
     */
    MEMBER_DEDUCTION(2, Boolean.TRUE),

    /**
     * 满减
     */
    FULL_REDUCTION(3, Boolean.FALSE),

    /**
     * 消费返利
     */
    CONSUMPTION_REBATE(4, Boolean.TRUE),

    /**
     * 兑换券
     */
    COUPON_EXCHANGE(12, Boolean.TRUE);


    @EnumValue
    private final Integer value;

    /**
     * 是否是平台优惠
     */
    private final boolean platform;

    //根据 value 获取枚举
    public static DiscountSourceType getByValue(Integer value) {
        for (DiscountSourceType type : DiscountSourceType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
