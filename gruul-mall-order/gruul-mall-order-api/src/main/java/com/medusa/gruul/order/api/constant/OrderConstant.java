package com.medusa.gruul.order.api.constant;

/**
 * <AUTHOR>
 * date 2022/6/9
 */
public interface OrderConstant {

    String ORDER_CACHE_PREFIX = "gruul:mall:order";
    /**
     * 创建订单缓存前缀
     * 订单key =>  gruul:mall:order:create:{userId}:{orderNo}
     */
    String ORDER_CACHE_KEY = ORDER_CACHE_PREFIX + ":create";

    /**
     * 小程序运力信息缓存key
     */
    String ORDER_MINI_DELIVER_CACHE_KEY = ORDER_CACHE_PREFIX + ":mini:delivery";
    /**
     * 修改订单的分布式锁信息
     * 完整key => lock:order:edit:{orderNo}
     */
    String ORDER_EDIT_LOCK_KEY = "lock:order:edit";

    /**
     * 操作订单包裹的锁
     */
    String ORDER_PACKAGE_LOCK = "order:package:lock";


    /**
     * 店铺订单备注sql 模板
     */
    String ORDER_REMARK_SQL_TEMPLATE = "remark = CONCAT('({})',remark)";

    /**
     * 订单超时时间 缓存key
     */
    String ORDER_TIMEOUT_CACHE = ORDER_CACHE_PREFIX + ":timeout";

    /**
     * 订单超时时间分布式锁
     */
    String ORDER_TIMEOUT_LOCK = "order:timeout:lock";

    /**
     * 订单表单设置
     */

    /**
     * 店铺交易信息缓存前缀
     */
    String ORDER_FORM_LOCK = "order:form:lock";


    /**
     * 店铺交易信息缓存前缀
     */
    String ORDER_FORM_CACHE = ORDER_CACHE_PREFIX + ":form";


    /**
     * 操作门店订单包裹的锁
     */
    String ORDER_STORE_PACKAGE_LOCK = "order:store:package:lock";

    /**
     * 平台发货名称
     */
    String PLATFORM_NAME = "平台";

    /**
     * 取餐码缓存 key ,
     * 取餐码每日重置一次，从一开始 依次递增
     */
    String PICKUP_CODE_CACHE = ORDER_CACHE_PREFIX + ":pickup:code";
    /**
     * 平台备注remark key
     */
    String PLATFORM_REMARK_KEY="platformRemark";
    /**
     * 供应商备注remark key
     */
    String SUPPLIER_REMARK_KEY="supplierRemark";
    /**
     * 店铺备注remark key
     */
    String SHOP_REMARK_KEY="remark";


}
