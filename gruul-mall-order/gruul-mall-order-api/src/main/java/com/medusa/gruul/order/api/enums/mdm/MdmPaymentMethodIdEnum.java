package com.medusa.gruul.order.api.enums.mdm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.medusa.gruul.common.model.enums.PayType;

/**
 * MDM支付方式枚举
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Getter
@AllArgsConstructor
public enum MdmPaymentMethodIdEnum {

    /**
     * 聚合支付
     */
    AGGREGATED(-1, "聚合支付"),

    /**
     * 会员支付
     */
    MEMBER(-2, "会员支付"),

    /**
     * 现金支付
     */
    CASH(-3, "现金支付"),

    /**
     * 余额支付
     */
    BALANCE(-4, "余额支付"),

    /**
     * 微信支付
     */
    WECHAT(-5, "微信支付"),

    /**
     * 支付宝
     */
    ALIPAY(-6, "支付宝支付");

    /**
     * 来源值
     */
    private final Integer value;

    /**
     * 来源描述
     */
    private final String description;

    /**
     * PayType转MdmPaymentMethodIdEnum
     */
    public static MdmPaymentMethodIdEnum fromPayType(PayType payType) {
        if (payType == null) {
            return null;
        }
        return switch (payType) {
            case BALANCE -> BALANCE;
            case WECHAT -> WECHAT;
            case ALIPAY -> ALIPAY;
            case MEMBER_CARD -> MEMBER;
            case AGGREGATION -> AGGREGATED;
            case ZERO_BUY -> BALANCE; // 0元购映射到余额支付
        };
    }

}
