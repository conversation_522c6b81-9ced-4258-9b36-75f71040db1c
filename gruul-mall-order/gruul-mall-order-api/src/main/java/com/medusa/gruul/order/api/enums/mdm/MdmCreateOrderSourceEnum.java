package com.medusa.gruul.order.api.enums.mdm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MDM订单来源枚举
 */
@Getter
@AllArgsConstructor
public enum MdmCreateOrderSourceEnum {
    
    /**
     * 退款订单
     */
    REFUND_ORDER("0", "REFUND_ORDER", "退款订单"),
    
    /**
     * 门店收银
     */
    STORE_ORDER("1", "STORE_ORDER", "门店收银"),
    
    /**
     * 美团
     */
    MEI_TUAN_ORDER("2", "MEI_TUAN_ORDER", "美团"),
    
    /**
     * 饿了么
     */
    ELE_ME_ORDER("3", "ELE_ME_ORDER", "饿了么"),
    
    /**
     * 会员充值
     */
    MEMBER_RECHARGE_ORDER("4", "MEMBER_RECHARGE_ORDER", "会员充值"),
    
    /**
     * 快速收银
     */
    FAST_PAY_ORDER("5", "FAST_PAY_ORDER", "快速收银"),
    
    /**
     * 私域商城
     */
    PRIVATE_MALL_ORDER("6", "PRIVATE_MALL_ORDER", "私域商城");
    
    /**
     * 来源值
     */
    private final String value;
    
    /**
     * 来源名称
     */
    private final String name;
    
    /**
     * 来源描述
     */
    private final String description;
    
    /**
     * 根据来源值获取枚举
     *
     * @param value 来源值
     * @return 订单来源枚举
     */
    public static MdmCreateOrderSourceEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (MdmCreateOrderSourceEnum source : values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return null;
    }
    
    /**
     * 根据来源名称获取枚举
     *
     * @param name 来源名称
     * @return 订单来源枚举
     */
    public static MdmCreateOrderSourceEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (MdmCreateOrderSourceEnum source : values()) {
            if (source.getName().equals(name)) {
                return source;
            }
        }
        return null;
    }
    
    /**
     * 获取来源描述
     *
     * @param value 来源值
     * @return 来源描述
     */
    public static String getDescription(String value) {
        MdmCreateOrderSourceEnum source = getByValue(value);
        return source == null ? null : source.getDescription();
    }
} 