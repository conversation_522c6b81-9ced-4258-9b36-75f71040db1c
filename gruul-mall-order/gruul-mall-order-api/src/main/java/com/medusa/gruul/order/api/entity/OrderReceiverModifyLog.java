package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 订单收货地址修改日志
 *
 * <AUTHOR>
 * @version 1.0, 2025/08/06
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@TableName(value = "t_order_receiver_modify_log", autoResultMap = true)
public class OrderReceiverModifyLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 原收货地址ID
     */
    private Long oldReceiverId;

    /**
     * 新收货地址ID
     */
    private Long newReceiverId;

    private Integer oldAddressVersion;

    private Integer newAddressVersion;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人类型
     */
    private OperatorType operatorType;

    /**
     * 修改时订单状态
     */
    private String orderStatus;

    /**
     * 修改阶段
     */
    private ModifyStage modifyStage;

    /**
     * 原地址信息快照
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private String oldAddressInfo;

    /**
     * 新地址信息快照
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private String newAddressInfo;

    /**
     * 操作人类型枚举
     */
    public enum OperatorType {
        BUYER, SELLER, ADMIN
    }

    /**
     * 修改阶段枚举
     */
    public enum ModifyStage {
        PRE_DELIVERY,  // 待发货
        IN_TRANSIT     // 待收货
    }
}