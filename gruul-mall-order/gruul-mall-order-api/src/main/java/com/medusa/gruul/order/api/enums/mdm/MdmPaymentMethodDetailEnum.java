package com.medusa.gruul.order.api.enums.mdm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.medusa.gruul.common.model.enums.PayType;

/**
 * MDM支付方式枚举
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Getter
@AllArgsConstructor
public enum MdmPaymentMethodDetailEnum {
    /**
     * 自定义
     */
    CUSTOM(0, "自定义"),

    /**
     * 现金支付
     */
    CASH(1, "现金支付"),

    /**
     * 会员支付
     */
    MEMBER(2, "会员支付"),

    /**
     * 聚合支付
     */
    AGGREGATED(3, "聚合支付"),

    /**
     * 余额
     */
    BALANCE(4, "余额"),

    /**
     * 微信
     */
    WECHAT(5, "微信"),

    /**
     * 支付宝
     */
    ALIPAY(6, "支付宝"),

    /**
     * 私域商城自定义
     */
    PRIVATE_MALL_PAYMENT(10, "私域商城自定义");

    /**
     * 来源值
     */
    private final Integer value;

    /**
     * 来源描述
     */
    private final String description;

    /**
     * PayType转MdmPaymentMethodDetailEnum
     */
    public static MdmPaymentMethodDetailEnum fromPayType(PayType payType) {
        if (payType == null) {
            return CUSTOM;
        }
        return switch (payType) {
            case BALANCE -> BALANCE;
            case WECHAT -> WECHAT;
            case ALIPAY -> ALIPAY;
            case MEMBER_CARD -> MEMBER;
            case AGGREGATION -> AGGREGATED;
            case ZERO_BUY -> BALANCE;
        };
    }
}
