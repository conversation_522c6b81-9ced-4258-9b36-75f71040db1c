package com.medusa.gruul.order.api.model;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * 店铺订单备注模板
 *
 * <AUTHOR>
 * date 2022/10/26
 */
public class ShopOrderFormModel extends LinkedHashMap<String, String> {

    public final static String REMARK_KEY = "remark";
    public final static String NOTIFY_KEY = "newOrderNotify";

    /**
     * 获取备注信息
     * 将表单里的所有内容取出来，key用括号包上，value直接拼接在后面，多个键值对用逗号隔开
     * 格式：(key1)value1,(key2)value2,(key3)value3
     */
    public static String remarkFirst(String remarkStr) {
        if (StrUtil.isEmpty(remarkStr)) {
            return null;
        }
        JSONObject remarkJson;
        try {
            remarkJson = JSON.parseObject(remarkStr);
        } catch (Exception ex) {
            return null;
        }
        if (remarkJson.isEmpty()) {
            return null;
        }

        // 移除系统内部字段
        remarkJson.remove(ShopOrderFormModel.NOTIFY_KEY);
        remarkJson.remove(ShopOrderFormModel.REMARK_KEY);

        // 构建格式化的备注字符串
        StringBuilder remarkBuilder = new StringBuilder();
        Set<Map.Entry<String, Object>> entries = remarkJson.entrySet();

        for (Map.Entry<String, Object> entry : entries) {
            String formName = entry.getKey();
            Object value = entry.getValue();

            // 跳过空值
            if (StrUtil.isEmptyIfStr(value)) {
                continue;
            }

            // 如果不是第一个元素，添加逗号分隔符
            if (!remarkBuilder.isEmpty()) {
                remarkBuilder.append(",");
            }

            // 格式：(key)value
            remarkBuilder.append("(").append(formName).append(")")
                        .append(value instanceof String str ? str : value.toString());
        }

        return !remarkBuilder.isEmpty() ? remarkBuilder.toString() : null;
    }

    /**
     * 备注模板初始化
     */
    public ShopOrderFormModel init(Boolean newOrderNotify) {
        this.clear();
        this.setNewOrderNotify(newOrderNotify);
        return this;
    }

    public boolean getNewOrderNotify() {
        return BooleanUtil.toBoolean(this.get(NOTIFY_KEY));
    }

    public ShopOrderFormModel setNewOrderNotify(boolean newOrderNotify) {
        this.put(NOTIFY_KEY, BooleanUtil.toStringTrueFalse(newOrderNotify));
        return this;
    }

    public String getRemark() {
        return StrUtil.trim(this.get(REMARK_KEY));
    }

    public ShopOrderFormModel setRemark(String remark) {
        this.put(REMARK_KEY, remark);
        return this;
    }


}
