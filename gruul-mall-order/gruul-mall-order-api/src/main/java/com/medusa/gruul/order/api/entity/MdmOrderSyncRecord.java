package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.medusa.gruul.common.ipaas.model.order.MdmOrderSyncDTO;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.order.api.enums.MdmOrderSyncStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_mdm_order_sync_record")
public class MdmOrderSyncRecord extends BaseEntity {
    @Serial
    private static final long serialVersionUID = -5716370238471062340L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 售后订单号
     */
    private String afsOrderNo;

    /**
     * 同步请求体
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 同步响应体
     */
    @TableField("response_body")
    private String responseBody;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private MdmOrderSyncDTO extra;

    /**
     * 同步状态 0-待同步 1-同步成功 2-同步失败
     */
    private MdmOrderSyncStatusEnum status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;
} 