package com.medusa.gruul.order.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 优惠来源系统枚举
 * 
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum DiscountSourceOrigin {

    /**
     * 内部系统
     */
    INTERNAL(0, "内部系统"),

    /**
     * 会员中台
     */
    MEMBER_CENTER(1, "会员中台");

    @EnumValue
    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     */
    public static DiscountSourceOrigin of(Integer value) {
        if (value == null) {
            return null;
        }
        for (DiscountSourceOrigin origin : values()) {
            if (origin.getValue().equals(value)) {
                return origin;
            }
        }
        return null;
    }
}