package com.medusa.gruul.order.api.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.order.api.enums.DistributeRecordType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0, 2025/6/5
 */
@Data
@Accessors(chain = true)
public class MemberConsumptionDistributeDTO implements Serializable {
	@Serial
	private static final long serialVersionUID = 5572783425618702581L;


	/**
	 * 运营主体
	 */
	private String operSubjectGuid;

	private String orderNumber;

	private String operatorTelName;

	/**
	 * 分销记录类型-1:未退款（订单完成）1:整单退款，0:部分退款
	 */
	private DistributeRecordType distributeRecordType;

	private String consumptionGuid;

	/**
	 * 新消费记录GUID ---主要用在退款的事件中才有
	 */
	private String newConsumptionGuid;


	/**
	 * 会员GUID
	 */
	private String memberInfoGuid;

	/**
	 * 会员持卡GUID
	 */
	private String memberInfoCardGuid;

	/**
	 * 主卡编号
	 */
	private String cardNum;

	/**
	 * 消费企业GUID
	 */
	private String enterpriseGuid;

	/**
	 * 消费门店GUID
	 */
	private String storeGuid;

	private String stallGuid;

	/**
	 * 消费门店名称
	 */
	private String storeName;

	/**
	 * 订单金额
	 */
	private BigDecimal orderAmount;

	/**
	 * 订单实付金额
	 */
	private BigDecimal orderPaidAmount;

	/**
	 * 订单优惠金额，无优惠为0
	 */
	private BigDecimal orderDiscountAmount;

	/**
	 * 卡支付金额
	 */
	private BigDecimal cardBalancePayAmount;

	/**
	 * 卡剩余余额
	 */
	private BigDecimal cardResidualBalance;

	/**
	 * 消费类型，0充值，1消费
	 *
	 */
	private Integer consumptionType;

	/**
	 * 消费时间
	 */
	@JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime consumptionTime;

	/**
	 * 订单guid（外部订单guid）
	 */
	private String orderGuid;

	/**
	 * 银行交易流水号（赚餐储值卡充值为交易号）
	 */
	private String bankTransactionId;


	/**
	 * 下单时间
	 */
	@JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime orderTime;

	/**
	 * 来源，0微信，2 一体机,1POS
	 */
	private Integer orderSource;

	/**
	 * 是否被撤销:0 否，1 是
	 */
	private Integer isCancel;


	/**
	 * 备注
	 */
	private String remark;


	/**
	 * 是否超额
	 **/
	private Integer excess;

	/**
	 * 0超额次数；1超额金额
	 */
	private Integer excessType;

	/**
	 * 超额金额/次数
	 */
	private BigDecimal excessValue;


	/**
	 * 设备id
	 */
	private String deviceId;

	/**
	 * 退款时间
	 */
	@JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime refundTime;

	/**
	 * 所属单位
	 */
	private String workName;

	/**
	 * 所属部门
	 */
	private String departmentName;

	/**
	 * 累计退款金额
	 */
	private BigDecimal addRefundAmount;

	/**
	 * 退款方式 0部分退  1整单退
	 */
	private Integer refundType;
}