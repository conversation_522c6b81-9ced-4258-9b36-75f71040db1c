<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-parent</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-web</artifactId>

    <dependencies>
        <!--   validator     -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-validator</artifactId>
            <version>${gruul-parent.version}</version>
        </dependency>
        <!-- global -->
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-config</artifactId>
        </dependency>
        <!-- model -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-model</artifactId>
        </dependency>
        <!-- web 排除tomcat 容器  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <!-- 去掉Jackson依赖，用fastjson2 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!-- valid -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-websockets-jsr</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-encrypt</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>