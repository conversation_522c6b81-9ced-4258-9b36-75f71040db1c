<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-parent</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-security</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>gruul-common-security-model</module>
        <module>gruul-common-security-client</module>
        <module>gruul-common-security-server</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-i18n</artifactId>
        </dependency>
    </dependencies>
</project>