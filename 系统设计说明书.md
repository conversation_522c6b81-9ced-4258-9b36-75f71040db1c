# SB2B2C电商平台系统设计说明书

## 1. 项目概述

### 1.1 项目简介
SB2B2C（Supply-Business-Business-Consumer）是一个基于Spring Cloud微服务架构的多租户电商平台，支持供应商、商家和消费者的全链路电商业务。

### 1.2 技术栈
- **开发语言**: Java 17
- **框架**: Spring Boot 2.x, Spring Cloud
- **数据库**: MySQL
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **搜索引擎**: Elasticsearch
- **网关**: Spring Cloud Gateway
- **注册中心**: Nacos
- **分布式锁**: Redisson
- **ORM**: MyBatis Plus
- **任务调度**: XXL-Job

## 2. 系统架构

### 2.1 整体架构
系统采用微服务架构，包含32个服务模块，分为核心服务和插件服务两大类：

#### 2.1.1 核心服务模块
- **gruul-mall-gateway**: API网关服务
- **gruul-mall-uaa**: 认证与授权服务
- **gruul-mall-user**: 用户管理服务
- **gruul-mall-shop**: 店铺管理服务
- **gruul-mall-goods**: 商品管理服务
- **gruul-mall-order**: 订单管理服务
- **gruul-mall-payment**: 支付服务
- **gruul-mall-cart**: 购物车服务
- **gruul-mall-storage**: 仓储管理服务
- **gruul-mall-search**: 搜索服务
- **gruul-mall-afs**: 售后服务
- **gruul-mall-live**: 直播服务
- **gruul-mall-overview**: 数据概览服务
- **gruul-mall-carrier-pigeon**: 消息中心服务
- **gruul-mall-open-platform**: 开放平台服务

#### 2.1.2 插件服务模块
- **addon-coupon**: 优惠券模块
- **addon-distribute**: 分销模块
- **addon-full-reduction**: 满减模块
- **addon-integral**: 积分模块
- **addon-member**: 会员模块
- **addon-seckill**: 秒杀模块
- **addon-bargain**: 砍价模块
- **addon-team**: 拼团模块
- **addon-supplier**: 供应商模块
- **addon-shop-store**: 店铺门店模块
- **addon-freight**: 运费模块
- **addon-invoice**: 发票模块
- **addon-live**: 直播插件
- **addon-matching-treasure**: 套餐模块
- **addon-rebate**: 消费返利模块
- **addon-platform**: 平台管理模块
- **addon-intra-city-distribution**: 同城配送模块

### 2.2 服务治理
- **服务注册与发现**: 基于Nacos实现服务注册与发现
- **配置管理**: 使用Nacos Config进行统一配置管理
- **负载均衡**: Spring Cloud LoadBalancer
- **熔断降级**: Sentinel
- **API网关**: Spring Cloud Gateway提供统一入口

## 3. 数据架构

### 3.1 多租户设计
系统支持多租户架构，通过以下方式实现数据隔离：

```java
// 租户配置
@ConfigurationProperties(prefix = "gruul.tenant")
public class TenantConfigProperties {
    private Boolean enableMultiProvider = Boolean.FALSE;  // 多服务商模式
    private Boolean enableMultiShop = Boolean.FALSE;      // 多店铺模式
    private String platformId = "platform_id";           // 平台ID字段
    private String shopId = "shop_id";                    // 店铺ID字段
}
```

### 3.2 数据库设计
- **主数据库**: MySQL，存储业务核心数据
- **连接池**: HikariCP，配置如下：
  - 连接超时: 15秒
  - 最大连接数: 45
  - 最大生命周期: 28000秒

### 3.3 分库分表策略
支持基于ShardingSphere的分库分表：

```java
public interface ShardingHelper {
    static DbTableNum dbTableNum(int db, int table) {
        return new DbTableNum(db, table);
    }
    
    record DbTableNum(int db, int table) {
        public int db(long shardingId) {
            return (int) shardingId % db;
        }
        public int table(long shardingId) {
            return (int) shardingId % table;
        }
    }
}
```

## 4. 安全架构

### 4.1 认证机制
系统采用JWT Token认证，支持多种登录方式：

#### 4.1.1 支持的认证方式
- **用户名密码登录**: `grant_type=password`
- **短信验证码登录**: `grant_type=sms_code`
- **微信小程序登录**: `grant_type=wechat_mini_app`
- **切换店铺登录**: `grant_type=switch_shop`

#### 4.1.2 JWT配置
```yaml
spring:
  security:
    server:
      token-expired: 2h                    # Token过期时间
      repeat-login: true                   # 允许重复登录
    client:
      payload-key: 1aXTEPkAhu7fh8e1T94qPw== # 加密密钥
```

### 4.2 权限控制
基于角色和权限的访问控制（RBAC）：

#### 4.2.1 角色定义
- **SUPER_ADMIN**: 超级管理员
- **PLATFORM_ADMIN**: 平台管理员
- **SHOP_ADMIN**: 店铺管理员
- **SUPPLIER_ADMIN**: 供应商管理员
- **USER**: 普通用户

#### 4.2.2 权限注解示例
```java
@PreAuthorize("""
    @SS.platform('order')
      .shop('order:delivery')
      .otherRoles(@S.R.SHOP_STORE,@S.R.SUPPLIER_ADMIN,@S.USER)
      .match()
""")
```

## 5. 消息架构

### 5.1 消息中间件
使用RabbitMQ作为消息中间件，支持：
- **异步消息处理**
- **事件驱动架构**
- **服务解耦**

### 5.2 消息配置
```yaml
spring:
  rabbitmq:
    host: IP
    port: 5673
    username: ${spring.profiles.active}stomp
    password: ${spring.profiles.active}stompadmin
    virtual-host: /${spring.profiles.active}stomp
    listener:
      simple:
        acknowledge-mode: manual  # 手动确认
        retry:
          enabled: true
          max-attempts: 1
```

### 5.3 主要交换机和队列
- **订单交换机**: `order.exchange`
- **商品交换机**: `goods.exchange`
- **店铺交换机**: `shop.exchange`
- **用户交换机**: `uaa.exchange`
- **支付交换机**: `payment.exchange`

## 6. 缓存架构

### 6.1 Redis配置
```yaml
spring:
  data:
    redis:
      database: 9
      host: IP
      port: 6378
      timeout: 60s
      lettuce:
        pool:
          max-idle: 3
          max-active: 3
```

### 6.2 分布式锁
基于Redisson实现分布式锁：

```java
@Redisson(value = "distribute:product:bind:lock", key = "#productId")
public void bindProduct(Long productId) {
    // 业务逻辑
}
```

### 6.3 缓存策略
- **本地缓存**: 使用Spring Cache
- **分布式缓存**: Redis
- **缓存穿透**: 布隆过滤器
- **缓存雪崩**: 随机过期时间
- **缓存击穿**: 分布式锁

## 7. 搜索架构

### 7.1 Elasticsearch配置
```yaml
easy-es:
  enable: true
  address: IP:9199
  username: elastic
  password: elastic
  global-config:
    distributed: true
    db-config:
      index-prefix: ${spring.profiles.active}-3.0.0_
```

### 7.2 搜索功能
- **商品搜索**: 支持全文检索、分类筛选、价格排序
- **店铺搜索**: 支持店铺名称、地理位置搜索
- **订单搜索**: 支持订单号、商品名称搜索

## 8. 监控与运维

### 8.1 任务调度
使用XXL-Job进行分布式任务调度：

```yaml
xxl-job:
  admin:
    admin-addresses: http://IP:9010/xxl-job-admin
  executor:
    id: 15
    access-token: 3l8jomjpbjti
```

### 8.2 日志管理
- **日志框架**: Logback
- **日志级别**: DEBUG（开发环境）
- **日志输出**: 控制台 + 文件

### 8.3 健康检查
- **服务健康检查**: Spring Boot Actuator
- **数据库连接检查**: HikariCP监控
- **Redis连接检查**: Lettuce监控

## 9. 部署架构

### 9.1 容器化部署
每个服务都提供Dockerfile，支持Docker容器化部署：

```dockerfile
FROM openjdk:17-jre-slim
COPY target/*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 9.2 环境配置
- **开发环境**: dev
- **测试环境**: test  
- **生产环境**: prod

### 9.3 服务端口分配
- **网关服务**: 9999
- **认证服务**: 8181
- **用户服务**: 8180
- **商品服务**: 9104
- **订单服务**: 8183
- **支付服务**: 8184

## 10. 扩展性设计

### 10.1 插件化架构
系统采用插件化设计，支持功能模块的热插拔：

```java
@AddonMethod(returnType = Long.class, arg1Filter = true)
Long earning(EarningType type, EarningParam param);
```

### 10.2 开放平台
提供开放API，支持第三方系统集成：

```java
@ConfigurationProperties(prefix = "open.auth")
public class OpenPlatformAuthProperties {
    private Boolean enable = true;
    private Map<String, String> keys;  // API密钥配置
    private Long expireTime = 300L;    // 签名有效时间
}
```

### 10.3 多端支持
- **管理端**: Web管理后台
- **商家端**: 商家管理系统
- **用户端**: 小程序、H5、APP
- **供应商端**: 供应商管理系统

## 11. 性能优化

### 11.1 数据库优化
- **读写分离**: 支持主从数据库配置
- **连接池优化**: HikariCP参数调优
- **索引优化**: 基于业务场景建立合适索引

### 11.2 缓存优化
- **多级缓存**: 本地缓存 + 分布式缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 基于事件驱动的缓存更新机制

### 11.3 异步处理
- **消息队列**: 异步处理耗时操作
- **线程池**: 自定义线程池处理并发任务
- **事件驱动**: 基于Spring Events的异步事件处理

---

*本文档基于项目代码分析生成，详细的API文档和部署指南请参考各服务模块的README文件。*
